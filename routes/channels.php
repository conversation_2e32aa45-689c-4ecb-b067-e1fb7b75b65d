<?php

use App\Models\ConversationThread;
use App\Models\User;
use Illuminate\Support\Facades\Broadcast;
use Vinkla\Hashids\Facades\Hashids;

Broadcast::channel('chat.{uuid}', function (User $user, string $hiddenId) {
    $id = Hashids::decode($hiddenId)[0];
    $thread = ConversationThread::findOrFail($id);

    if (! $thread->hasParticipant($user->id)) {
        return false;
    }

    return true;
});
