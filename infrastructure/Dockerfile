FROM dunglas/frankenphp

RUN install-php-extensions \
    @composer \
    pcntl \
    gd \
    bcmath \
    igbinary \
    sockets \
    exif \
    imagick \
    redis \
    pdo_pgsql \
    excimer

# Копируем конфигурацию PHP
COPY infrastructure/php.ini /usr/local/etc/php/php.ini

COPY . /app

# Install the dependencies
RUN composer install --ignore-platform-reqs --no-dev -a
#RUN php artisan migrate --force

# Запуск FrankenPHP с настройками для больших файлов
ENTRYPOINT ["php", "artisan", "octane:frankenphp"]
