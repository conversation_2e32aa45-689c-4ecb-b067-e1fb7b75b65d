<?php

namespace App\Providers;

use App\Events\PostChangeEvent;
use App\Events\PostView;
use App\Events\UserRegisteredEvent;
use App\Listeners\PostChangeHandle;
use App\Listeners\PostViewHandle;
use App\Listeners\TransactionCreatedHandler;
use App\Listeners\UserRegisteredHandler;
use Bavix\Wallet\Internal\Events\TransactionCreatedEventInterface;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Event::listen([
            PostChangeEvent::class => PostChangeHandle::class,
            PostView::class => PostViewHandle::class,
            //            TransactionCreatedEventInterface::class => TransactionCreatedHandler::class,
            UserRegisteredEvent::class => UserRegisteredHandler::class,
        ]);
    }
}
