<?php

namespace App\Providers;

use App\Interfaces\VerificationServiceInterface;
use App\Services\Verification\VerificationService;
use Illuminate\Support\ServiceProvider;

class VerificationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Регистрируем интерфейс для инъекции зависимостей
        $this->app->bind(VerificationServiceInterface::class, function ($app) {
            return $app->make(VerificationService::class);
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
