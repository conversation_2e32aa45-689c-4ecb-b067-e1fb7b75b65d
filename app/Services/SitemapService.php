<?php

namespace App\Services;

use App\Models\Category;
use App\Models\News;
use App\Models\Post;
use App\Models\RefCity;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use XMLWriter;

class SitemapService
{
    /**
     * Базовый URL сайта
     */
    protected string $baseUrl;

    /**
     * Диск для хранения sitemap файлов
     */
    protected string $disk;

    /**
     * Путь для хранения sitemap файлов
     */
    protected string $storagePath;

    /**
     * Конструктор
     */
    public function __construct()
    {
        $this->baseUrl = 'https://gunpost.ru';
        $this->disk = 's3';
        $this->storagePath = 'sitemap';
    }

    /**
     * Генерация всех sitemap файлов
     */
    public function generateAll(): void
    {
        $this->generateAdsSitemap();
        $this->generateCategoriesSitemap();
        $this->generateCategoriesTypesSitemap();
        $this->generateGeoCategoriesSitemap();
        $this->generateGeoCategoriesTypesSitemap();
        $this->generateImagesSitemap();
        $this->generateNewsSitemap();
        $this->generateSitemapIndex();
        $this->pingSearchEngines();
    }

    /**
     * Отправка уведомлений поисковым системам о новом sitemap
     */
    public function pingSearchEngines(): void
    {
        $sitemapUrl = $this->baseUrl.'/sitemap.xml';
        if (env('APP_ENV') === 'local') {
            return;
        }

        try {
            // Пинг Google
            Http::get('https://www.google.com/ping?sitemap='.urlencode($sitemapUrl));

            // Пинг Яндекс
            Http::get('https://webmaster.yandex.ru/ping?sitemap='.urlencode($sitemapUrl));

        } catch (\Exception $e) {
            Log::error('Failed to ping search engines: '.$e->getMessage());
        }
    }

    /**
     * Генерация sitemap для объявлений
     */
    public function generateAdsSitemap(): void
    {
        // Текущие объявления (обновленные за последние 15 минут)
        $fifteenMinutesAgo = Carbon::now()->subMinutes(15);
        $today = Carbon::now()->format('Y-m-d-H\hi');
        $recentPosts = Post::whereNull('archived_at')
            ->whereNotNull('published_at')
            ->where('updated_at', '>=', $fifteenMinutesAgo)
            ->get();

        $this->generateAdsSitemapFile($recentPosts, "ads/ads-{$today}-15m.xml");

        // Объявления за последние 7 дней
        $lastWeekPosts = Post::whereNull('archived_at')
            ->whereNotNull('published_at')
            ->whereDate('updated_at', '>=', Carbon::now()->subDays(7))
            ->whereDate('updated_at', '<', Carbon::now()->today())
            ->get();

        $lastWeekDate = Carbon::now()->format('Y-m-d');
        $this->generateAdsSitemapFile($lastWeekPosts, "ads/ads-{$lastWeekDate}.xml");

        // Объявления за последние 8-30 дней
        $olderPosts = Post::whereNull('archived_at')
            ->whereNotNull('published_at')
            ->whereDate('updated_at', '>=', Carbon::now()->subDays(30))
            ->whereDate('updated_at', '<', Carbon::now()->subDays(7))
            ->get();

        $olderDate = Carbon::now()->subDays(30)->format('Y-m-d');
        $this->generateAdsSitemapFile($olderPosts, "ads/ads-{$olderDate}.xml");

        // Архивные объявления (по кварталам)
        $currentQuarter = Carbon::now()->format('Y-Q');
        $archivedPosts = Post::whereNotNull('archived_at')
            ->whereNotNull('published_at')
            ->whereDate('updated_at', '>=', Carbon::now()->subDays(90)) // Только за последние 90 дней
            ->get();

        $this->generateAdsSitemapFile($archivedPosts, "ads/ads-archive-{$currentQuarter}.xml", true);
    }

    /**
     * Генерация sitemap файла для объявлений
     */
    protected function generateAdsSitemapFile($posts, string $filename, bool $isArchived = false): void
    {
        $xml = new XMLWriter;
        $xml->openMemory();
        $xml->startDocument('1.0', 'UTF-8');
        $xml->setIndent(true);
        $xml->startElement('urlset');
        $xml->writeAttribute('xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9');
        $xml->writeAttribute('xmlns:image', 'http://www.google.com/schemas/sitemap-image/1.1');

        foreach ($posts as $post) {
            $xml->startElement('url');
            $xml->writeElement('loc', $this->baseUrl.'/post/'.$post->slug);

            $lastmod = $isArchived ? $post->archived_at : $post->updated_at;
            $xml->writeElement('lastmod', $lastmod->format('Y-m-d\TH:i:sP'));

            $priority = $isArchived ? '0.3' : '0.8';
            $xml->writeElement('priority', $priority);

            $changefreq = $isArchived ? 'monthly' : 'daily';
            $xml->writeElement('changefreq', $changefreq);

            // Добавляем изображения к объявлению (Google рекомендует добавлять их в тот же URL)
            if ($post->media && $post->media->count() > 0) {
                // Добавляем до 20 изображений для каждого объявления
                $mediaItems = $post->media->take(20);
                foreach ($mediaItems as $media) {
                    $xml->startElement('image:image');
                    $xml->writeElement('image:loc', $media->getUrl('full'));
                    $xml->writeElement('image:title', $post->title);
                    if ($post->description) {
                        $xml->writeElement('image:caption', Str::limit($post->description, 150));
                    }
                    $xml->endElement(); // image:image
                }
            }

            $xml->endElement(); // url
        }

        $xml->endElement(); // urlset
        $xml->endDocument();

        $content = $xml->outputMemory();

        Storage::put("{$this->storagePath}/{$filename}", $content);
    }

    /**
     * Генерация sitemap для категорий
     */
    public function generateCategoriesSitemap(): void
    {
        $categories = Category::all();

        // Создаем отдельные файлы для основных категорий
        foreach ($categories as $category) {
            if (! $category->parent_id) { // Только главные категории
                $categoryXml = new XMLWriter;
                $categoryXml->openMemory();
                $categoryXml->startDocument('1.0', 'UTF-8');
                $categoryXml->setIndent(true);
                $categoryXml->startElement('urlset');
                $categoryXml->writeAttribute('xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9');

                $categoryXml->startElement('url');
                $categoryXml->writeElement('loc', $this->baseUrl.'/'.$category->slug);
                $categoryXml->writeElement('lastmod', Carbon::now()->format('Y-m-d\TH:i:sP'));
                $categoryXml->writeElement('changefreq', 'weekly');
                $categoryXml->writeElement('priority', '0.8');
                $categoryXml->endElement(); // url

                $categoryXml->endElement(); // urlset
                $categoryXml->endDocument();

                $categoryContent = $categoryXml->outputMemory();
                Storage::put("{$this->storagePath}/categories/{$category->slug}.xml", $categoryContent);
            }
        }
    }

    /**
     * Генерация sitemap для комбинаций город + категория
     */
    public function generateGeoCategoriesSitemap(): void
    {
        // Получаем топ-30 городов по количеству объявлений
        $topCities = RefCity::where('order', '>', 0)
            ->orderBy('order', 'desc')
            ->get();

        // Получаем все категории
        $categories = Category::all();

        foreach ($topCities as $city) {
            foreach ($categories as $category) {
                // Получаем количество объявлений в этой комбинации
                $query = Post::whereNull('archived_at')
                    ->whereNotNull('published_at')
                    ->where('ref_city_id', $city->id)
                    ->where('category_id', $category->id);

                $postsCount = $query->count();

                // Если есть хотя бы одно объявление, создаем sitemap
                if ($postsCount > 0) {
                    $xml = new XMLWriter;
                    $xml->openMemory();
                    $xml->startDocument('1.0', 'UTF-8');
                    $xml->setIndent(true);
                    $xml->startElement('urlset');
                    $xml->writeAttribute('xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9');

                    $xml->startElement('url');
                    $xml->writeElement('loc', $this->baseUrl.'/'.$city->slug.'/'.$category->slug);
                    $xml->writeElement('lastmod', Carbon::now()->format('Y-m-d\TH:i:sP'));
                    $xml->writeElement('changefreq', 'daily');
                    $xml->writeElement('priority', '0.7');
                    $xml->endElement(); // url

                    $xml->endElement(); // urlset
                    $xml->endDocument();

                    $content = $xml->outputMemory();
                    Storage::put("{$this->storagePath}/geo-categories/{$city->slug}-{$category->slug}.xml", $content);
                }
            }
        }
    }

    /**
     * Генерация sitemap для комбинаций город + категория + тип
     */
    public function generateGeoCategoriesTypesSitemap(): void
    {
        // Получаем города, в которых есть объявления
        $topCities = RefCity::whereHas('posts', function ($query) {
            $query->whereNull('archived_at')
                ->whereNotNull('published_at');
        })
            ->take(30)
            ->get();

        // Получаем все категории
        $categories = Category::all();

        foreach ($topCities as $city) {
            foreach ($categories as $category) {
                // Получаем все типы для этой категории
                $types = \App\Models\RefType::where('category_id', $category->id)
                    ->whereExists(function ($query) {
                        $query->select(\DB::raw(1))
                            ->from('post_attributes')
                            ->whereRaw('post_attributes.attributeable_id = ref_types.id')
                            ->where('post_attributes.attributeable_type', 'App\\Models\\RefType');
                    })
                    ->get();

                foreach ($types as $type) {
                    // Получаем количество объявлений в этой комбинации
                    $postsCount = Post::whereNull('archived_at')
                        ->whereNotNull('published_at')
                        ->where('ref_city_id', $city->id)
                        ->where('category_id', $category->id)
                        ->whereHas('attributes', function ($query) use ($type) {
                            $query->where('attributeable_type', 'App\\Models\\RefType')
                                ->where('attributeable_id', $type->id);
                        })
                        ->count();

                    // Если есть хотя бы 5 объявлений, создаем sitemap
                    if ($postsCount >= 5) {
                        $xml = new XMLWriter;
                        $xml->openMemory();
                        $xml->startDocument('1.0', 'UTF-8');
                        $xml->setIndent(true);
                        $xml->startElement('urlset');
                        $xml->writeAttribute('xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9');

                        $xml->startElement('url');
                        $xml->writeElement('loc', $this->baseUrl.'/'.$city->slug.'/'.$category->slug.'/'.$type->slug);
                        $xml->writeElement('lastmod', Carbon::now()->format('Y-m-d\TH:i:sP'));
                        $xml->writeElement('changefreq', 'daily');
                        $xml->writeElement('priority', '0.6');
                        $xml->endElement(); // url

                        $xml->endElement(); // urlset
                        $xml->endDocument();

                        $content = $xml->outputMemory();
                        Storage::put("{$this->storagePath}/geo-cat-types/{$city->slug}-{$category->slug}-{$type->slug}.xml", $content);
                    }
                }
            }
        }
    }

    /**
     * Генерация sitemap для подкатегорий (типов)
     */
    public function generateCategoriesTypesSitemap(): void
    {
        // Получаем все категории
        $categories = Category::all();

        foreach ($categories as $category) {
            // Получаем все типы для этой категории
            $types = \App\Models\RefType::where('category_id', $category->id)
                ->whereExists(function ($query) {
                    $query->select(\DB::raw(1))
                        ->from('post_attributes')
                        ->whereRaw('post_attributes.attributeable_id = ref_types.id')
                        ->where('post_attributes.attributeable_type', 'App\\Models\\RefType');
                })
                ->get();

            if ($types->count() > 0) {
                $xml = new XMLWriter;
                $xml->openMemory();
                $xml->startDocument('1.0', 'UTF-8');
                $xml->setIndent(true);
                $xml->startElement('urlset');
                $xml->writeAttribute('xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9');

                foreach ($types as $type) {
                    // Получаем количество объявлений для этого типа
                    $postsCount = Post::whereNull('archived_at')
                        ->whereNotNull('published_at')
                        ->where('category_id', $category->id)
                        ->whereHas('attributes', function ($query) use ($type) {
                            $query->where('attributeable_type', 'App\\Models\\RefType')
                                ->where('attributeable_id', $type->id);
                        })
                        ->count();

                    if ($postsCount > 0) {
                        $xml->startElement('url');
                        $xml->writeElement('loc', $this->baseUrl.'/'.$category->slug.'/'.$type->slug);
                        $xml->writeElement('lastmod', Carbon::now()->format('Y-m-d\TH:i:sP'));
                        $xml->writeElement('changefreq', 'daily');
                        $xml->writeElement('priority', '0.7');
                        $xml->endElement(); // url
                    }
                }

                $xml->endElement(); // urlset
                $xml->endDocument();

                $content = $xml->outputMemory();
                Storage::put("{$this->storagePath}/categories-types/{$category->slug}-types.xml", $content);
            }
        }
    }

    /**
     * Генерация sitemap для изображений
     */
    public function generateImagesSitemap(): void
    {
        $currentMonth = Carbon::now()->format('Y-m');

        // Получаем все изображения объявлений за последние 90 дней
        $posts = Post::whereNull('archived_at')
            ->whereNotNull('published_at')
            ->whereDate('updated_at', '>=', Carbon::now()->subDays(90))
            ->with('media')
            ->get();

        $xml = new XMLWriter;
        $xml->openMemory();
        $xml->startDocument('1.0', 'UTF-8');
        $xml->setIndent(true);
        $xml->startElement('urlset');
        $xml->writeAttribute('xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9');
        $xml->writeAttribute('xmlns:image', 'http://www.google.com/schemas/sitemap-image/1.1');

        foreach ($posts as $post) {
            if ($post->media && $post->media->count() > 0) {
                $xml->startElement('url');
                $xml->writeElement('loc', $this->baseUrl.'/post/'.$post->slug);

                // Добавляем до 20 изображений для каждого объявления
                $mediaItems = $post->media->take(20);
                foreach ($mediaItems as $media) {
                    $xml->startElement('image:image');
                    $xml->writeElement('image:loc', $media->getUrl('full'));
                    $xml->writeElement('image:title', $post->title);
                    if ($post->description) {
                        $xml->writeElement('image:caption', Str::limit($post->description, 150));
                    }
                    $xml->endElement(); // image:image
                }

                $xml->endElement(); // url
            }
        }

        $xml->endElement(); // urlset
        $xml->endDocument();

        $content = $xml->outputMemory();

        Storage::put("{$this->storagePath}/images/images-{$currentMonth}.xml", $content);
    }

    /**
     * Генерация sitemap для новостей/блога
     */
    public function generateNewsSitemap(): void
    {
        $news = News::whereNotNull('published_at')->get();

        $xml = new XMLWriter;
        $xml->openMemory();
        $xml->startDocument('1.0', 'UTF-8');
        $xml->setIndent(true);
        $xml->startElement('urlset');
        $xml->writeAttribute('xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9');
        $xml->writeAttribute('xmlns:news', 'http://www.google.com/schemas/sitemap-news/0.9');

        foreach ($news as $item) {
            $xml->startElement('url');
            $xml->writeElement('loc', $this->baseUrl.'/news/'.$item->slug);
            $xml->writeElement('lastmod', $item->updated_at->format('Y-m-d\TH:i:sP'));
            $xml->writeElement('changefreq', 'weekly');
            $xml->writeElement('priority', '0.6');

            $xml->startElement('news:news');
            $xml->startElement('news:publication');
            $xml->writeElement('news:name', config('app.name'));
            $xml->writeElement('news:language', 'ru');
            $xml->endElement(); // news:publication
            $xml->writeElement('news:publication_date', $item->published_at->format('Y-m-d\TH:i:sP'));
            $xml->writeElement('news:title', $item->title);
            $xml->endElement(); // news:news

            $xml->endElement(); // url
        }

        $xml->endElement(); // urlset
        $xml->endDocument();

        $content = $xml->outputMemory();

        Storage::put("{$this->storagePath}/news-blog.xml", $content);
    }

    /**
     * Генерация индексного sitemap файла
     */
    public function generateSitemapIndex(): void
    {
        // Создаем пустые директории, если их нет
        $directories = ['ads', 'categories', 'categories-types', 'geo-categories', 'geo-cat-types', 'images'];
        foreach ($directories as $dir) {
            if (! Storage::exists("{$this->storagePath}/{$dir}")) {
                Storage::makeDirectory("{$this->storagePath}/{$dir}");
            }
        }

        $xml = new XMLWriter;
        $xml->openMemory();
        $xml->startDocument('1.0', 'UTF-8');
        $xml->setIndent(true);
        $xml->startElement('sitemapindex');
        $xml->writeAttribute('xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9');

        // Добавляем sitemap для объявлений (обновленных за последние 15 минут)
        $today = Carbon::now()->format('Y-m-d-H\hi');
        $xml->startElement('sitemap');
        $xml->writeElement('loc', $this->baseUrl."/{$this->storagePath}/ads/ads-{$today}-15m.xml");
        $xml->writeElement('lastmod', Carbon::now()->format('Y-m-d\TH:i:sP'));
        $xml->endElement(); // sitemap

        // Добавляем sitemap для объявлений за последние 7 дней
        $lastWeekDate = Carbon::now()->format('Y-m-d');
        $xml->startElement('sitemap');
        $xml->writeElement('loc', $this->baseUrl."/{$this->storagePath}/ads/ads-{$lastWeekDate}.xml");
        $xml->writeElement('lastmod', Carbon::now()->format('Y-m-d\TH:i:sP'));
        $xml->endElement(); // sitemap

        // Добавляем sitemap для объявлений за последние 8-30 дней
        $olderDate = Carbon::now()->subDays(30)->format('Y-m-d');
        $xml->startElement('sitemap');
        $xml->writeElement('loc', $this->baseUrl."/{$this->storagePath}/ads/ads-{$olderDate}.xml");
        $xml->writeElement('lastmod', Carbon::now()->format('Y-m-d\TH:i:sP'));
        $xml->endElement(); // sitemap

        // Добавляем sitemap для архивных объявлений
        $currentQuarter = Carbon::now()->format('Y-Q');
        $xml->startElement('sitemap');
        $xml->writeElement('loc', $this->baseUrl."/{$this->storagePath}/ads/ads-archive-{$currentQuarter}.xml");
        $xml->writeElement('lastmod', Carbon::now()->format('Y-m-d\TH:i:sP'));
        $xml->endElement(); // sitemap

        // Добавляем sitemap для категорий
        $categories = Category::all();
        foreach ($categories as $category) {
            if (! $category->parent_id) { // Только главные категории
                $xml->startElement('sitemap');
                $xml->writeElement('loc', $this->baseUrl."/{$this->storagePath}/categories/{$category->slug}.xml");
                $xml->writeElement('lastmod', Carbon::now()->format('Y-m-d\TH:i:sP'));
                $xml->endElement(); // sitemap

                // Добавляем файл с типами для этой категории
                if (Storage::exists("{$this->storagePath}/categories-types/{$category->slug}-types.xml")) {
                    $xml->startElement('sitemap');
                    $xml->writeElement('loc', $this->baseUrl."/{$this->storagePath}/categories-types/{$category->slug}-types.xml");
                    $xml->writeElement('lastmod', Carbon::now()->format('Y-m-d\TH:i:sP'));
                    $xml->endElement(); // sitemap
                }
            }
        }

        // Добавляем sitemap для комбинаций город + категория
        $geoCategoriesFiles = Storage::files("{$this->storagePath}/geo-categories");
        foreach ($geoCategoriesFiles as $file) {
            $xml->startElement('sitemap');
            $xml->writeElement('loc', $this->baseUrl.'/'.$file);
            $xml->writeElement('lastmod', Carbon::now()->format('Y-m-d\TH:i:sP'));
            $xml->endElement(); // sitemap
        }

        // Добавляем sitemap для комбинаций город + категория + тип
        $geoCatTypesFiles = Storage::files("{$this->storagePath}/geo-cat-types");
        foreach ($geoCatTypesFiles as $file) {
            $xml->startElement('sitemap');
            $xml->writeElement('loc', $this->baseUrl.'/'.$file);
            $xml->writeElement('lastmod', Carbon::now()->format('Y-m-d\TH:i:sP'));
            $xml->endElement(); // sitemap
        }

        // Добавляем sitemap для изображений
        $currentMonth = Carbon::now()->format('Y-m');
        $xml->startElement('sitemap');
        $xml->writeElement('loc', $this->baseUrl."/{$this->storagePath}/images/images-{$currentMonth}.xml");
        $xml->writeElement('lastmod', Carbon::now()->format('Y-m-d\TH:i:sP'));
        $xml->endElement(); // sitemap

        // Добавляем sitemap для новостей
        $xml->startElement('sitemap');
        $xml->writeElement('loc', $this->baseUrl."/{$this->storagePath}/news-blog.xml");
        $xml->writeElement('lastmod', Carbon::now()->format('Y-m-d\TH:i:sP'));
        $xml->endElement(); // sitemap

        $xml->endElement(); // sitemapindex
        $xml->endDocument();

        $content = $xml->outputMemory();

        Storage::put("{$this->storagePath}/sitemap_index.xml", $content);
    }
}
