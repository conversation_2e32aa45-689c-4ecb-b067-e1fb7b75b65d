<?php

namespace App\Services;

use App\Models\PaymentWebhook;
use App\Models\Promotion;
use Bavix\Wallet\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Robokassa\Robokassa;

class RobokassaService
{
    protected $robokassa;

    /**
     * @throws \Exception
     */
    public function __construct()
    {
        $this->robokassa = new Robokassa(config('services.robokassa'));
    }

    /**
     * @throws \Exception
     */
    public function PromotionPayment(Promotion $promotion): array
    {
        return $this->payment($promotion->post->user, $promotion->price, $promotion->getMetaProduct());
    }

    /**
     * Универсальный метод для создания платежа
     *
     * @param  $user  - пользователь, который платит
     * @param  $amount  - сумма
     * @param  array  $meta  - дополнительные метаданные
     *
     * @throws \Exception
     */
    public function payment($user, $amount, array $meta = []): array
    {
        $transaction = $user->deposit($amount, $meta, false);

        $params = [
            'OutSum' => $transaction->amount,
            'InvoiceID' => $transaction->id,
            'Description' => $meta['title'],
            'Receipt' => [
                'items' => [
                    [
                        'name' => $meta['description'] ?? $meta['title'],
                        'quantity' => 1,
                        'sum' => $transaction->amount,
                        'payment_method' => 'full_payment',
                        'payment_object' => 'commodity',
                        'tax' => 'none',
                    ],
                ],
            ],
        ];

        $paymentUrl = $this->robokassa->sendPaymentRequestCurl($params);

        return [
            'success' => true,
            'url' => $paymentUrl,
        ];
    }

    public function handleWebhook(Request $request): string
    {
        try {
            $invId = (int) $request->input('InvId');
            $transaction = Transaction::where('id', $invId)->firstOrFail();

            if (isset($transaction->meta['id'])) {
                $promotion = Promotion::findOrFail($transaction->meta['id']);
            } else {
                $promotion = null;
            }

            PaymentWebhook::firstOrCreate([
                'gateway' => 'robokassa',
                'amount' => (int) $request->input('OutSum'),
                'invoice_id' => $invId,
                'transaction_id' => $transaction->id,
                'data' => $request->all(),
            ]);

            $status = $this->robokassa->opState($request->input('InvId'));

            if ($status['Result']['Code'] !== 0) {
                $transaction->payable->confirm($transaction);

                if ($promotion) {
                    $promotion->purchased_at = Carbon::now();
                    $promotion->save();

                    $transaction->payable->pay($promotion);
                }

                return "OK{$request->InvId}\n";
            } else {
                return "ERROR{$request->InvId}\n";
            }
        } catch (\Throwable $e) {
            Log::error('Robokassa webhook error', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return "ERROR{$request->InvId}\n";
        }
    }
}
