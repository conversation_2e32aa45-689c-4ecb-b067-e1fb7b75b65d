<?php

namespace App\Services;

use App\Models\Index\IndexPost;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class SearchService
{
    // Массив транслитерации для оружия
    private const WEAPON_TRANSLIT_MAP = [
        // Гладкоствольные ружья
        'saiga' => 'сайга',
        'sayga' => 'сайга',
        'vepr' => 'вепрь',
        'toz' => 'тоз',
        'izh' => 'иж',
        'ij' => 'иж',
        'mp' => 'мп',
        'mr' => 'мр',
        'baikal' => 'байкал',
        'baykal' => 'байкал',

        // Популярные модели
        'groza' => 'гроза',
        'vzor' => 'взор',
        'bars' => 'барс',
        'berkut' => 'беркут',
        'sokol' => 'сокол',
        'yastreb' => 'ястреб',
        'orion' => 'орион',
        'taiga' => 'тайга',
        'tayga' => 'тайга',
        'shtorm' => 'шторм',
        'sturm' => 'штурм',
        'osa' => 'оса',
        'udar' => 'удар',
        'korshun' => 'коршун',
        'vityaz' => 'витязь',
        'strela' => 'стрела',
        'molot' => 'молот',
        'viking' => 'викинг',
        'tigr' => 'тигр',
        'los' => 'лось',
        'zubr' => 'зубр',
        'kedr' => 'кедр',
        'sever' => 'север',
        'taifun' => 'тайфун',
        'kobra' => 'кобра',
        'vostok' => 'восток',
        'zapad' => 'запад',
        'rys' => 'рысь',
        'sobol' => 'соболь',
        'gepard' => 'гепард',
        'bulat' => 'булат',
        'b;' => 'иж',

        // Производители
        'kalashnikov' => 'калашников',
        'molot-oruzhie' => 'молот-оружие',
        'molot-oruzie' => 'молот-оружие',
        'zlatoust' => 'златоуст',
        'tulsky' => 'тульский',
        'izhevsk' => 'ижевск',
        'izhmash' => 'ижмаш',
        'izhmeh' => 'ижмек',

        // Типы оружия
        'gladkostvolnoe' => 'гладкоствольное',
        'nareznoye' => 'нарезное',
        'nareznoe' => 'нарезное',
        'pnevmatika' => 'пневматика',
        'travmatika' => 'травматика',
        'karabin' => 'карабин',
        'vintovka' => 'винтовка',
        'pistolet' => 'пистолет',
        'revolver' => 'револьвер',
        'ruzhe' => 'ружье',
        'ruzhyo' => 'ружьё',
    ];

    private const BASIC_TRANSLIT_MAP = [
        'a' => 'а', 'b' => 'б', 'v' => 'в', 'g' => 'г', 'd' => 'д',
        'e' => 'е', 'yo' => 'ё', 'zh' => 'ж', 'z' => 'з', 'i' => 'и',
        'y' => 'й', 'k' => 'к', 'l' => 'л', 'm' => 'м', 'n' => 'н',
        'o' => 'о', 'p' => 'п', 'r' => 'р', 's' => 'с', 't' => 'т',
        'u' => 'у', 'f' => 'ф', 'h' => 'х', 'ts' => 'ц', 'ch' => 'ч',
        'sh' => 'ш', 'sch' => 'щ', 'y' => 'ы', 'e' => 'э', 'yu' => 'ю',
        'ya' => 'я',
    ];

    // Настройки скоринга
    private const SCORE_EXACT_MATCH = 100000;

    private const SCORE_STARTS_WITH = 50000;

    private const SCORE_CONTAINS_PHRASE = 20000;

    private const SCORE_ALL_WORDS_MATCH = 10000;

    private const SCORE_PARTIAL_WORDS = 5000;

    private const SCORE_DESCRIPTION_MATCH = 1000;

    private const SCORE_TRANSLIT_BONUS = 2000;

    private const SCORE_WEAPON_MODEL_BONUS = 2000;

    private const PENALTY_ZIP_CATEGORY = 15000;

    private const CACHE_TTL = 300; // 5 минут

    private const SEARCH_LIMIT = 200;

    private const RESULT_LIMIT = 20;

    /**
     * Выполнить поиск с кешированием
     */
    public function search(?string $query, array $filters = []): Collection
    {
        $query = trim($query);

        // Если запрос пустой, возвращаем последние записи, отсортированные по promotion_start_date
        if (empty($query)) {
            // Кешируем и пустой запрос тоже, чтобы не бить по БД/ES
            $cacheKey = $this->generateCacheKey('__empty__', $filters);

            return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($filters) {
                return $this->fetchLatestPosts($filters);
            });
        }

        // Генерируем ключ кеша
        $cacheKey = $this->generateCacheKey($query, $filters);

        // Используем кеш
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($query, $filters) {
            return $this->performSearch($query, $filters);
        });
    }

    /**
     * Выполнить поиск без кеширования (для отладки)
     */
    public function searchWithoutCache(string $query, array $filters = []): Collection
    {
        $query = trim($query);

        if (empty($query)) {
            return $this->fetchLatestPosts($filters);
        }

        return $this->performSearch($query, $filters);
    }

    /**
     * Получить отладочную информацию о поиске
     */
    public function getDebugInfo(string $query, array $filters = []): array
    {
        $query = trim($query);
        $normalizedQuery = $this->normalizeQuery($query);
        $transliteratedQuery = $this->transliterateQuery($query);

        $posts = $this->searchWithoutCache($query, $filters);

        $debugPosts = $posts->map(function ($post) use ($normalizedQuery, $transliteratedQuery) {
            return [
                'id' => $post->id,
                'title' => $post->title,
                'category' => $post->category ?? 'unknown',
                'relevance_score' => $post->relevance_score,
                'promotion_start_date' => $post->promotion_start_date,
                'score_breakdown' => $this->getScoreBreakdown($post, $normalizedQuery, $transliteratedQuery),
            ];
        });

        return [
            'query' => $query,
            'normalized_query' => $normalizedQuery,
            'transliterated_query' => $transliteratedQuery,
            'filters' => $filters,
            'total_results' => $posts->count(),
            'results' => $debugPosts,
        ];
    }

    /**
     * Основная логика поиска
     */
    private function performSearch(string $searchQuery, array $filters): Collection
    {
        // Транслитерируем запрос если он на латинице
        $transliteratedQuery = $this->transliterateQuery($searchQuery);

        // Используем транслитерированный запрос для Scout поиска
        $scoutQuery = $transliteratedQuery !== $searchQuery ? $transliteratedQuery : $searchQuery;

        // Нормализуем поисковый запрос
        $normalizedQuery = $this->normalizeQuery($searchQuery);
        $normalizedTranslitQuery = $this->normalizeQuery($transliteratedQuery);
        $queryWords = $this->extractWords($normalizedQuery);
        $translitQueryWords = $this->extractWords($normalizedTranslitQuery);

        // Scout поиск с оригинальным И транслитерированным запросом
        $posts = IndexPost::search($scoutQuery.' '.$searchQuery)
            ->query(function ($query) {
                $query->where('published_at', '!=', null);
            });

        // Применяем фильтры
        if (! empty($filters['city_slug'])) {
            $posts = $posts->where('city_slug', $filters['city_slug']);
        }

        if (! empty($filters['category'])) {
            $posts = $posts->where('category', $filters['category']);
        }

        $posts = $posts
            ->where('archived', false)
            ->take(self::SEARCH_LIMIT)
            ->get();

        // Если с учетом city_slug ничего не нашли, пробуем без него
        if ($posts->isEmpty() && ! empty($filters['city_slug'])) {
            $posts = IndexPost::search($scoutQuery.' '.$searchQuery)
                ->query(function ($query) {
                    $query->where('published_at', '!=', null);
                })
                // не применяем city_slug здесь
                ->when(! empty($filters['category']), function ($q) use ($filters) {
                    return $q->where('category', $filters['category']);
                })
                ->where('archived', false)
                ->take(self::SEARCH_LIMIT)
                ->get();
        }

        // Расчет релевантности для каждого поста
        $posts = $posts->map(function ($post) use ($normalizedQuery, $queryWords, $normalizedTranslitQuery, $translitQueryWords) {
            $post->relevance_score = $this->calculateRelevance(
                $post,
                $normalizedQuery,
                $queryWords,
                $normalizedTranslitQuery,
                $translitQueryWords
            );

            return $post;
        });

        // Сортировка по релевантности
        $posts = $posts->sortByDesc(function ($post) {
            return $post->relevance_score;
        });

        // Группировка по уровням релевантности
        $grouped = $this->groupByRelevanceLevel($posts);

        // Собираем финальный результат
        $finalPosts = collect();
        foreach ($grouped as $group) {
            $finalPosts = $finalPosts->concat(
                $group->sortByDesc('promotion_start_date')->values()
            );
        }

        return $finalPosts->take(self::RESULT_LIMIT);
    }

    /**
     * Получить последние записи при пустом поисковом запросе
     */
    private function fetchLatestPosts(array $filters): Collection
    {
        $query = IndexPost::query()
            ->where('published_at', '!=', null)
            ->where('archived', false);

        if (! empty($filters['city_slug'])) {
            $query->where('city_slug', $filters['city_slug']);
        }

        if (! empty($filters['category'])) {
            $query->where('category', $filters['category']);
        }

        $result = $query
            ->orderBy('promotion_start_date', 'desc')
            ->take(self::RESULT_LIMIT)
            ->get();

        // Если с учетом city_slug ничего не нашли, пробуем без него
        if ($result->isEmpty() && ! empty($filters['city_slug'])) {
            $query = IndexPost::query()
                ->where('published_at', '!=', null)
                ->where('archived', false);

            if (! empty($filters['category'])) {
                $query->where('category', $filters['category']);
            }

            $result = $query
                ->orderBy('promotion_start_date', 'desc')
                ->take(self::RESULT_LIMIT)
                ->get();
        }

        return $result;
    }

    /**
     * Рассчитать релевантность поста
     */
    private function calculateRelevance($post, string $normalizedQuery, array $queryWords, string $normalizedTranslitQuery, array $translitQueryWords): float
    {
        $title = $this->normalizeQuery($post->title);
        $description = $this->normalizeQuery($post->description ?? '');

        $score = 0;
        $promotionBonus = $post->promotion_start_date ?
            (strtotime($post->promotion_start_date) > time() ? 100 : 50) : 0;

        // Штраф для категории 'zip' (запчасти)
        $categoryPenalty = 0;
        if (isset($post->category) && $post->category === 'zip') {
            $categoryPenalty = self::PENALTY_ZIP_CATEGORY;
        }

        // 1. Точное совпадение всего заголовка
        if ($title === $normalizedQuery || $title === $normalizedTranslitQuery) {
            return self::SCORE_EXACT_MATCH + $promotionBonus - $categoryPenalty;
        }

        // 2. Заголовок начинается с запроса
        if (str_starts_with($title, $normalizedQuery) || str_starts_with($title, $normalizedTranslitQuery)) {
            $score += self::SCORE_STARTS_WITH;
        }

        // 3. Заголовок содержит точную фразу
        $containsOriginal = str_contains($title, $normalizedQuery);
        $containsTranslit = str_contains($title, $normalizedTranslitQuery);

        if ($containsOriginal || $containsTranslit) {
            $score += self::SCORE_CONTAINS_PHRASE;

            // Бонус за позицию фразы
            if ($containsOriginal) {
                $position = strpos($title, $normalizedQuery);
                $score += max(0, 1000 - $position * 10);
            }
            if ($containsTranslit) {
                $position = strpos($title, $normalizedTranslitQuery);
                $score += max(0, 1000 - $position * 10);
            }
        }

        // 4. Все слова из запроса есть в заголовке
        $titleWords = $this->extractWords($title);
        $matchScore = $this->calculateWordMatchScore($queryWords, $translitQueryWords, $titleWords);
        $score += $matchScore;

        // 5. Проверка описания
        if (str_contains($description, $normalizedQuery) || str_contains($description, $normalizedTranslitQuery)) {
            $score += self::SCORE_DESCRIPTION_MATCH;
        }

        // 6. Учет длины заголовка
        $lengthPenalty = min(strlen($title) / 100, 5) * 10;
        $score -= $lengthPenalty;

        // 7. Учет расстояния Левенштейна
        $levenshteinScore = $this->calculateLevenshteinScore($normalizedQuery, $normalizedTranslitQuery, $title);
        $score += $levenshteinScore;

        // 8. Проверка моделей оружия
        if ($this->compareWeaponModels($normalizedQuery, $title) ||
            $this->compareWeaponModels($normalizedTranslitQuery, $title)) {
            $score += self::SCORE_WEAPON_MODEL_BONUS;
        }

        return $score + $promotionBonus - $categoryPenalty;
    }

    /**
     * Рассчитать score для совпадения слов
     */
    private function calculateWordMatchScore(array $queryWords, array $translitQueryWords, array $titleWords): float
    {
        $score = 0;

        // Проверка оригинальных слов
        $matchedWords = 0;
        $wordPositionScore = 0;
        foreach ($queryWords as $queryWord) {
            if (in_array($queryWord, $titleWords)) {
                $matchedWords++;
                $wordPos = array_search($queryWord, $titleWords);
                $wordPositionScore += max(0, 100 - $wordPos * 5);
            }
        }

        // Проверка транслитерированных слов
        $translitMatchedWords = 0;
        $translitWordPositionScore = 0;
        foreach ($translitQueryWords as $queryWord) {
            if (in_array($queryWord, $titleWords)) {
                $translitMatchedWords++;
                $wordPos = array_search($queryWord, $titleWords);
                $translitWordPositionScore += max(0, 100 - $wordPos * 5);
            }
        }

        // Используем лучший результат
        $bestMatchedWords = max($matchedWords, $translitMatchedWords);
        $bestWordPositionScore = max($wordPositionScore, $translitWordPositionScore);
        $totalQueryWords = max(count($queryWords), count($translitQueryWords));

        if ($bestMatchedWords === $totalQueryWords && $totalQueryWords > 0) {
            $score += self::SCORE_ALL_WORDS_MATCH + $bestWordPositionScore;
        } elseif ($totalQueryWords > 0) {
            $score += ($bestMatchedWords / $totalQueryWords) * self::SCORE_PARTIAL_WORDS;
        }

        return $score;
    }

    /**
     * Рассчитать score на основе расстояния Левенштейна
     */
    private function calculateLevenshteinScore(string $query1, string $query2, string $title): float
    {
        $levenshteinDistance = min(
            levenshtein(
                mb_substr($query1, 0, 255),
                mb_substr($title, 0, 255)
            ),
            levenshtein(
                mb_substr($query2, 0, 255),
                mb_substr($title, 0, 255)
            )
        );

        if ($levenshteinDistance <= 3) {
            return max(0, 3000 - $levenshteinDistance * 500);
        }

        return 0;
    }

    /**
     * Транслитерировать запрос
     */
    private function transliterateQuery(string $query): string
    {
        $lowerQuery = mb_strtolower($query);

        // Сначала проверяем специфичные названия оружия
        foreach (self::WEAPON_TRANSLIT_MAP as $latin => $cyrillic) {
            if (str_contains($lowerQuery, $latin)) {
                $query = str_ireplace($latin, $cyrillic, $query);
            }
        }

        // Затем применяем базовую транслитерацию если остались латинские буквы
        if (preg_match('/[a-z]/i', $query)) {
            $query = strtr($query, self::BASIC_TRANSLIT_MAP);
        }

        return $query;
    }

    /**
     * Группировать по уровням релевантности
     */
    private function groupByRelevanceLevel(Collection $posts): array
    {
        $groups = [
            'exact' => collect(),      // 90000+
            'high' => collect(),       // 50000-89999
            'medium' => collect(),     // 10000-49999
            'low' => collect(),        // 1000-9999
            'minimal' => collect(),    // < 1000
        ];

        foreach ($posts as $post) {
            if ($post->relevance_score >= 90000) {
                $groups['exact']->push($post);
            } elseif ($post->relevance_score >= 50000) {
                $groups['high']->push($post);
            } elseif ($post->relevance_score >= 10000) {
                $groups['medium']->push($post);
            } elseif ($post->relevance_score >= 1000) {
                $groups['low']->push($post);
            } else {
                $groups['minimal']->push($post);
            }
        }

        // Дополнительная сортировка внутри групп: сначала не-zip, потом zip
        foreach ($groups as $key => $group) {
            $groups[$key] = $group->sortBy(function ($post) {
                return ($post->category === 'zip') ? 1 : 0;
            })->values();
        }

        return $groups;
    }

    /**
     * Нормализовать запрос
     */
    private function normalizeQuery(string $query): string
    {
        $query = mb_strtolower(trim($query));
        $query = preg_replace('/\s+/', ' ', $query);
        $query = preg_replace('/[^\p{L}\p{N}\s]/u', '', $query);

        return $query;
    }

    /**
     * Извлечь слова из текста
     */
    private function extractWords(string $text): array
    {
        $words = preg_split('/\s+/', $text, -1, PREG_SPLIT_NO_EMPTY);

        return array_values(array_filter($words, function ($word) {
            return mb_strlen($word) >= 2;
        }));
    }

    /**
     * Нормализовать модель оружия
     */
    private function normalizeWeaponModel(string $text): string
    {
        $text = mb_strtolower($text);
        $text = preg_replace('/[-_]/', ' ', $text);
        $text = preg_replace('/\s+/', ' ', trim($text));

        return $text;
    }

    /**
     * Сравнить модели оружия
     */
    private function compareWeaponModels(string $query, string $title): bool
    {
        $normalizedQuery = $this->normalizeWeaponModel($query);
        $normalizedTitle = $this->normalizeWeaponModel($title);

        $queryVariants = [
            $normalizedQuery,
            str_replace(' ', '-', $normalizedQuery),
            str_replace(' ', '', $normalizedQuery),
        ];

        foreach ($queryVariants as $variant) {
            if (str_contains($normalizedTitle, $variant)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Генерировать ключ кеша
     */
    private function generateCacheKey(string $query, array $filters): string
    {
        $filterString = implode('_', array_filter($filters));

        return 'search:'.md5($query.$filterString);
    }

    /**
     * Получить breakdown для отладки
     */
    private function getScoreBreakdown($post, string $normalizedQuery, string $transliteratedQuery): array
    {
        $title = $this->normalizeQuery($post->title);

        return [
            'exact_match' => $title === $normalizedQuery || $title === $transliteratedQuery,
            'starts_with' => str_starts_with($title, $normalizedQuery) || str_starts_with($title, $transliteratedQuery),
            'contains_phrase' => str_contains($title, $normalizedQuery) || str_contains($title, $transliteratedQuery),
            'title_length' => strlen($title),
            'category' => $post->category ?? 'unknown',
            'is_zip_category' => ($post->category === 'zip'),
            'levenshtein_distance' => min(
                levenshtein(
                    mb_substr($normalizedQuery, 0, 255),
                    mb_substr($title, 0, 255)
                ),
                levenshtein(
                    mb_substr($transliteratedQuery, 0, 255),
                    mb_substr($title, 0, 255)
                )
            ),
        ];
    }
}
