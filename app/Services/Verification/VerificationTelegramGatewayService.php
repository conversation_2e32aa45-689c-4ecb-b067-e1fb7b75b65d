<?php

namespace App\Services\Verification;

use App\Interfaces\VerificationServiceInterface;
use Illuminate\Support\Facades\Log;
use Somar<PERSON>esen\TelegramGateway\Facades\TelegramGateway;

class VerificationTelegramGatewayService implements VerificationServiceInterface
{
    private VerificationService $verificationService;

    public function __construct(VerificationService $verificationService)
    {
        $this->verificationService = $verificationService;
    }

    /**
     * Отправить код подтверждения через TelegramGateway
     *
     * @param  string  $phone  Номер телефона
     * @return bool Результат отправки
     *
     * @throws \Exception
     */
    public function sendCode(string $phone): bool
    {
        try {
            $phone = $this->verificationService->formatPhone($phone);
            $otp = $this->verificationService->generateCode($phone);

            TelegramGateway::sendVerificationMessage($phone, [
                'code' => $otp->token,
                'ttl' => 300,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Ошибка отправки кода через TelegramGateway: '.$e->getMessage());
            throw $e;
        }
    }

    /**
     * Проверить код подтверждения
     *
     * @param  string  $phone  Номер телефона
     * @param  string  $code  Код подтверждения
     * @return bool Результат проверки
     */
    public function verifyCode(string $phone, string $code): bool
    {
        return $this->verificationService->verifyCode($phone, $code);
    }
}
