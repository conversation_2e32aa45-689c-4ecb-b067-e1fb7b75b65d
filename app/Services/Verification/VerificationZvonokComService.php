<?php

namespace App\Services\Verification;

use App\Interfaces\VerificationServiceInterface;
use Ichtrojan\Otp\Models\Otp as OtpModel;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class VerificationZvonokComService implements VerificationServiceInterface
{
    private string $publicKey;

    private string $campaignId;

    private VerificationService $verificationService;

    public function __construct(VerificationService $verificationService)
    {
        $this->verificationService = $verificationService;
        $this->publicKey = config('services.zvonok.public_key');
        $this->campaignId = config('services.zvonok.campaign_id');
    }

    public function sendCode(string $phone): bool
    {
        if (config('app.env') === 'local') {
            OtpModel::create([
                'identifier' => $phone,
                'token' => 1234,
                'validity' => 15,
                'valid' => true,
            ]);

            return true;
        }

        try {
            $phone = $this->verificationService->formatPhone($phone);

            $response = Http::asMultipart()
                ->post('https://zvonok.com/manager/cabapi_external/api/v1/phones/flashcall/', [
                    'public_key' => $this->publicKey,
                    'phone' => $phone,
                    'campaign_id' => $this->campaignId,
                ]);

            $data = $response->json();

            if ($data['status'] === 'ok') {
                OtpModel::create([
                    'identifier' => $phone,
                    'token' => $data['data']['pincode'],
                    'validity' => 15,
                    'valid' => true,
                ]);

                return true;
            }

            Log::error('Zvonok.com API error', ['response' => $response->json()]);

            return false;
        } catch (\Exception $e) {
            Log::error('Zvonok.com API exception', ['error' => $e->getMessage()]);

            return false;
        }
    }

    /**
     * Проверить код подтверждения
     *
     * @param  string  $phone  Номер телефона
     * @param  string  $code  Код подтверждения
     * @return bool Результат проверки
     */
    public function verifyCode(string $phone, string $code): bool
    {
        // Используем метод из сервиса верификации
        return $this->verificationService->verifyCode($phone, $code);
    }
}
