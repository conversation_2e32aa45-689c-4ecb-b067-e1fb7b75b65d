<?php

namespace App\Services\Verification;

use App\Interfaces\VerificationServiceInterface;
use Ichtrojan\Otp\Otp;
use Illuminate\Support\Facades\Log;
use Propaganistas\LaravelPhone\PhoneNumber;

class VerificationService implements VerificationServiceInterface
{
    /**
     * Проверить код подтверждения
     *
     * @param  string  $phone  Номер телефона
     * @param  string  $code  Код подтверждения
     * @return bool Результат проверки
     */
    public function verifyCode(string $phone, string $code): bool
    {
        try {
            $phone = $this->formatPhone($phone);
            $validate = (new Otp)->validate($phone, $code);

            return $validate->status;
        } catch (\Exception $e) {
            Log::error('Ошибка проверки кода: '.$e->getMessage());

            return false;
        }
    }

    /**
     * Форматировать номер телефона в формат E.164
     *
     * @param  string  $phone  Номер телефона
     * @return string Отформатированный номер телефона
     */
    public function formatPhone(string $phone): string
    {
        return (new PhoneNumber($phone, 'RU'))->formatE164();
    }

    /**
     * Генерировать код подтверждения
     *
     * @param  string  $phone  Номер телефона
     * @param  int  $length  Длина кода
     * @return object Объект с кодом
     */
    public function generateCode(string $phone, int $length = 4): object
    {
        return (new Otp)->generate($phone, 'numeric', $length);
    }
}
