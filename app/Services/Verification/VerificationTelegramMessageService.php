<?php

namespace App\Services\Verification;

use App\Interfaces\VerificationServiceInterface;
use App\Models\User;
use App\Services\TelegramService;
use Illuminate\Support\Facades\Log;

class VerificationTelegramMessageService implements VerificationServiceInterface
{
    private VerificationService $verificationService;

    private TelegramService $telegramService;

    public function __construct(VerificationService $verificationService, TelegramService $telegramService)
    {
        $this->verificationService = $verificationService;
        $this->telegramService = $telegramService;
    }

    /**
     * Отправить код подтверждения в Telegram
     *
     * @param  string  $phone  Номер телефона пользователя
     * @return bool Результат отправки
     *
     * @throws \Exception
     */
    public function sendCode(string $phone): bool
    {
        try {
            // Форматируем номер телефона
            $phone = $this->verificationService->formatPhone($phone);

            // Получаем пользователя со связанным Telegram
            $user = User::where('phone', $phone)->whereHas('telegram')->first();

            if (! $user || ! $user->telegram || ! $user->telegram->chat_id) {
                Log::error('Не найден пользователь с Telegram для номера: '.$phone);

                return false;
            }

            // Генерируем код
            $otp = $this->verificationService->generateCode($phone);

            // Формируем сообщение
            $message = "Ваш код подтверждения: *{$otp->token}*";

            // Отправляем сообщение в Telegram
            if (! $this->telegramService->sendMessage($user->telegram->chat_id, $message)) {
                Log::error('Не удалось отправить сообщение в Telegram для пользователя: '.$user->id);

                return false;
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Ошибка отправки кода в Telegram: '.$e->getMessage());

            return false;
        }
    }

    /**
     * Проверить код подтверждения
     *
     * @param  string  $phone  Номер телефона
     * @param  string  $code  Код подтверждения
     * @return bool Результат проверки
     */
    public function verifyCode(string $phone, string $code): bool
    {
        return $this->verificationService->verifyCode($phone, $code);
    }
}
