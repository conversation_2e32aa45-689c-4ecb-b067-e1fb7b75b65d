<?php

namespace App\Services\Promotion\Handlers;

use App\Models\Post;
use App\Models\Promotion;
use App\Models\PromotionType;

interface PromotionHandlerInterface
{
    /**
     * Создание продвижения
     */
    public function create(Post $post, PromotionType $promotionType): Promotion;

    /**
     * Обработка продвижения после оплаты
     */
    public function process(Promotion $promotion): void;
}
