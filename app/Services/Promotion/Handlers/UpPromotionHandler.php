<?php

namespace App\Services\Promotion\Handlers;

use App\Models\Post;
use App\Models\Promotion;
use App\Models\PromotionType;
use Carbon\Carbon;

class UpPromotionHandler implements PromotionHandlerInterface
{
    /**
     * {@inheritDoc}
     */
    public function create(Post $post, PromotionType $promotionType): Promotion
    {
        return Promotion::create([
            'post_id' => $post->id,
            'promotion_type_id' => $promotionType->id,
            'price' => $promotionType->price,
            'duration' => $promotionType->duration,
            'expires_at' => Carbon::now()->addDays($promotionType->duration),
        ]);
    }

    /**
     * {@inheritDoc}
     */
    public function process(Promotion $promotion): void
    {
        $promotion->processed_at = Carbon::now();
        $promotion->save();
    }
}
