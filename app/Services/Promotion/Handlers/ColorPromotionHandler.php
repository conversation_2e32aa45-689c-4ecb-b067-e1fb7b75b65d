<?php

namespace App\Services\Promotion\Handlers;

use App\Models\Post;
use App\Models\Promotion;
use App\Models\PromotionType;
use Carbon\Carbon;

class ColorPromotionHandler implements PromotionHandlerInterface
{
    /**
     * {@inheritDoc}
     */
    public function create(Post $post, PromotionType $promotionType): Promotion
    {
        return Promotion::create([
            'post_id' => $post->id,
            'promotion_type_id' => $promotionType->id,
            'price' => $promotionType->price,
            'duration' => $promotionType->duration,
            'expires_at' => Carbon::now()->addDays($promotionType->duration),
        ]);
    }

    /**
     * {@inheritDoc}
     */
    public function process(Promotion $promotion): void
    {
        $upPromotionType = PromotionType::where('type', 'up')->firstOrFail();

        Promotion::create([
            'post_id' => $promotion->post_id,
            'promotion_type_id' => $upPromotionType->id,
            'price' => $upPromotionType->price,
            'duration' => $upPromotionType->duration,
            'expires_at' => Carbon::now()->addDays($upPromotionType->duration),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
            'processed_at' => Carbon::now(),
            'purchased_at' => Carbon::now(),
        ]);

        $promotion->processed_at = Carbon::now();
        $promotion->save();
    }
}
