<?php

namespace App\Services;

use Exception;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TextRuService
{
    protected string $userKey;

    public function __construct()
    {
        $this->userKey = config('services.text_ru.userkey');
    }

    // 1 этап: отправка текста на проверку
    public function sendText(string $text): ?string
    {
        try {
            $response = Http::asForm()->post('https://api.text.ru/post', [
                'userkey' => $this->userKey,
                'text' => $text,
                'exceptdomain' => 'gunpost.ru',
            ]);

            // Проверяем статус ответа
            $response->throw();

            $data = $response->json();

            if (! isset($data['text_uid'])) {
                Log::warning('TextRu API не вернул text_uid', ['response' => $data]);

                return null;
            }

            return $data['text_uid'];

        } catch (ConnectionException $e) {
            // Ошибка соединения с API
            Log::error('Ошибка соединения с TextRu API: '.$e->getMessage());

            return null;
        } catch (RequestException $e) {
            // Ошибка в запросе или ответе API
            Log::error('Ошибка запроса к TextRu API: '.$e->getMessage(), [
                'status' => $e->response->status(),
                'body' => $e->response->body(),
            ]);

            return null;
        } catch (Exception $e) {
            // Другие непредвиденные ошибки
            Log::error('Непредвиденная ошибка при запросе к TextRu API: '.$e->getMessage());

            return null;
        }
    }

    // 2 этап: получение результата по UID
    public function getResult(string $uid): ?array
    {
        try {
            $response = Http::asForm()->post('https://api.text.ru/post', [
                'userkey' => $this->userKey,
                'uid' => $uid,
                'exceptdomain' => 'gunpost.ru',
                'jsonvisible' => 'detail',
            ]);

            // Проверяем статус ответа
            $response->throw();

            $data = $response->json();

            if (! is_array($data)) {
                Log::warning('TextRu API вернул некорректный формат данных', ['response' => $response->body()]);

                return null;
            }

            // Обработка seo_check
            if (isset($data['seo_check']) && is_string($data['seo_check']) && $data['seo_check'] !== '') {
                try {
                    $seo = json_decode($data['seo_check'], true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        throw new Exception('Ошибка декодирования JSON: '.json_last_error_msg());
                    }

                    $data['seo_check'] = $seo;
                    // Выделяем отдельные параметры
                    $data['count_chars_with_space'] = $seo['count_chars_with_space'] ?? null;
                    $data['count_chars_without_space'] = $seo['count_chars_without_space'] ?? null;
                    $data['count_words'] = $seo['count_words'] ?? null;
                    $data['water_percent'] = $seo['water_percent'] ?? null;
                    $data['spam_percent'] = $seo['spam_percent'] ?? null;
                    $data['mixed_words'] = $seo['mixed_words'] ?? null;
                    $data['list_keys'] = $seo['list_keys'] ?? null;
                    $data['list_keys_group'] = $seo['list_keys_group'] ?? null;
                } catch (Exception $e) {
                    Log::warning('Ошибка при обработке seo_check: '.$e->getMessage(), [
                        'seo_check' => $data['seo_check'] ?? null,
                    ]);
                    $data['seo_check'] = null;
                }
            } else {
                $data['seo_check'] = null;
            }

            return $data;

        } catch (ConnectionException $e) {
            // Ошибка соединения с API
            Log::error('Ошибка соединения с TextRu API: '.$e->getMessage());

            return null;
        } catch (RequestException $e) {
            // Ошибка в запросе или ответе API
            Log::error('Ошибка запроса к TextRu API: '.$e->getMessage(), [
                'status' => $e->response->status(),
                'body' => $e->response->body(),
            ]);

            return null;
        } catch (Exception $e) {
            // Другие непредвиденные ошибки
            Log::error('Непредвиденная ошибка при запросе к TextRu API: '.$e->getMessage());

            return null;
        }
    }
}
