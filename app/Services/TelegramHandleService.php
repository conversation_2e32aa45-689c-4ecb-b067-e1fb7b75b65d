<?php

namespace App\Services;

use App\Events\PostChangeEvent;
use App\Models\Post;
use App\Models\RefModeration;
use App\Models\User;
use App\Models\UserTelegram;
use Illuminate\Support\Facades\Log;

class TelegramHandleService
{
    /**
     * Создание токена и ссылки на бота
     */
    public function generateStartLink(User $user): ?string
    {
        if (! $user->telegram) {
            $token = rand(100000, 999999);

            $user->telegram()->create([
                'user_id' => $user->id,
                'start_token' => $token,
            ]);
        } else {
            $token = $user->telegram->start_token;
        }

        $botName = config('services.telegram-bot-api.bot_name');

        return "https://t.me/$botName?start={$token}";
    }

    /**
     * Обработка обновления из Telegram (через webhook)
     */
    public function handleTelegramWebhook(array $update): void
    {
        // Обработка callback-запросов от кнопок
        if (isset($update['callback_query'])) {
            $this->handleCallbackQuery($update['callback_query']);

            return;
        }

        // Обработка обычных сообщений
        if (! isset($update['message']['text'])) {
            return;
        }

        if (! isset($update['message']['from'])) {
            return;
        }

        $text = $update['message']['text'];
        if (! str_starts_with($text, '/start')) {
            return;
        }

        $parts = explode(' ', $text);
        if (count($parts) < 2) {
            return;
        }

        $token = $parts[1];
        $chatId = $update['message']['chat']['id'];
        $from = $update['message']['from'];

        $userTelegram = UserTelegram::where('start_token', $token)->whereNull('username')->first();
        if (! $userTelegram) {
            return;
        }

        $userTelegram->update([
            'chat_id' => $chatId,
            'username' => $from['username'] ?? null,
            'first_name' => $from['first_name'] ?? null,
            'last_name' => $from['last_name'] ?? null,
            'language_code' => $from['language_code'] ?? null,
            'is_premium' => $from['is_premium'] ?? false,
        ]);

        if (! $userTelegram->user->name || $userTelegram->user->name === 'Пользователь') {
            $userTelegram->user->update([
                'name' => $from['first_name'] ?? null,
            ]);
        }

        // send ok to telegram
        $telegramService = new TelegramService;
        $telegramService->sendMessage($chatId, '✅ Вы успешно авторизовались');
    }

    /**
     * Обработка callback-запросов от кнопок
     */
    private function handleCallbackQuery(array $callbackQuery): void
    {
        $data = $callbackQuery['data'] ?? null;
        $chatId = $callbackQuery['message']['chat']['id'] ?? null;

        if (! $data || ! $chatId) {
            return;
        }

        // Разбираем данные callback
        $parts = explode(' ', $data);
        $action = $parts[0] ?? null;
        $postId = $parts[1] ?? null;

        if (! $action || ! $postId) {
            return;
        }

        // Находим пост
        $post = Post::withoutGlobalScopes()->find($postId);
        if (! $post) {
            Log::error('Пост не найден при обработке callback', ['post_id' => $postId]);

            return;
        }

        // Обрабатываем действие
        $telegramService = new TelegramService;

        switch ($action) {
            case 'approve_post':
                $post->moderation_id = RefModeration::IS_APPROVED;
                $post->save();
                // Отправляем событие для обновления индекса
                PostChangeEvent::dispatch($post);
                $telegramService->sendMessage($chatId, "✅ Объявление *{$post->title}* одобрено!");
                break;

            case 'ban_post':
                $post->moderation_id = RefModeration::IS_BANNED;
                $post->save();
                // Отправляем событие для обновления индекса
                PostChangeEvent::dispatch($post);
                $telegramService->sendMessage($chatId, "🚫 Объявление *{$post->title}* заблокировано!");
                break;

            default:
                Log::warning('Неизвестное действие в callback', ['action' => $action, 'post_id' => $postId]);
                break;
        }
    }
}
