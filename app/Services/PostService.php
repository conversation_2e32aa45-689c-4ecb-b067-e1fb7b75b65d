<?php

namespace App\Services;

use App\Http\Resources\CategoryResource;
use App\Http\Resources\CityResource;
use App\Http\Resources\PathSeoDataResource;
use App\Http\Resources\PostAttributeResource;
use App\Models\Category;
use App\Models\Index\IndexPost;
use App\Models\PathSeoData;
use App\Models\Post;
use App\Models\PostAttribute;
use App\Models\RefCity;
use App\Models\RefGunType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class PostService
{
    public function getUserPost(string $slug)
    {
        $user = auth('sanctum')->user();

        if ($user?->role === 'admin') {
            return Post::withoutGlobalScopes()
                ->where('slug', $slug)
                ->firstOrFail();
        }

        return Post::withoutGlobalScopes()
            ->where('slug', $slug)
            ->where('user_id', $user?->id)
            ->firstOrFail();
    }

    public function getPost(string $slug, Request $request): Post
    {
        $user = auth('sanctum')->user();

        if ($user?->role === 'admin') {
            return Post::withoutGlobalScopes()
                ->where('slug', $slug)
                ->firstOrFail();
        }

        if ($user?->id) {
            return Post::withoutGlobalScopes()
                ->where('slug', $slug)
                ->where('user_id', $user?->id)
                ->firstOrFail();
        }

        return Post::withoutGlobalScopes()
            ->where('slug', $slug)
            ->where('fingerprint', $request->uuid)
            ->firstOrFail();
    }

    public function getFilters($category, $is_filter = false): array
    {
        $category_slug = $category?->slug ?? '__';
        $filters = Category::getFiltersFor($category_slug, $is_filter);
        $cacheKey = 'filters_cat_'.$category_slug.'_filter_'.$is_filter.'_key_'.md5(json_encode($filters));

        return Cache::remember($cacheKey, now()->addHours(PathSeoData::CACHE_TIME), function () use ($filters, $category) {
            $result = [];

            foreach ($filters as $key => $value) {
                // Пропускаем, если не задан класс для values
                if (empty($value['values'])) {
                    continue;
                }

                $query = resolve($value['values'])::query()->select(['name', 'slug']);

                if ($key === 'types' && isset($category->id)) {
                    $query->where('category_id', $category->id);
                }

                $values = $query->get()
                    ->map(fn ($item) => [
                        'label' => $item->name,
                        'value' => $item->slug,
                    ])
                    ->toArray();

                // Пропускаем, если выборка пустая
                if (empty($values)) {
                    continue;
                }

                $result[$key] = array_filter([
                    ...$value,
                    'values' => $values,
                ]);
            }

            return array_filter($result);
        });
    }

    public function getSeoData($path)
    {
        if (! $path) {
            $path = '/';
        }

        return Cache::remember("seo_data_{$path}", now()->addHours(PathSeoData::CACHE_TIME), function () use ($path) {
            $seo = PathSeoData::where('path', $path)->first();

            return $seo ? new PathSeoDataResource($seo) : null;
        });
    }

    public function getCityData($citySlug)
    {
        if (! $citySlug) {
            return null;
        }

        return Cache::remember("city_data_{$citySlug}", now()->addHours(PathSeoData::CACHE_TIME), function () use ($citySlug) {
            $city = RefCity::where('slug', $citySlug)->first();

            return $city ? new CityResource($city) : null;
        });
    }

    public function getCategoryData($categorySlug)
    {
        if (! $categorySlug) {
            return null;
        }

        return Cache::remember("category_data_{$categorySlug}", now()->addHours(PathSeoData::CACHE_TIME), function () use ($categorySlug) {
            $category = Category::where('slug', $categorySlug)->first();

            return $category ? new CategoryResource($category) : null;
        });
    }

    public function getGunTypeData($typeSlug)
    {
        if (! $typeSlug) {
            return null;
        }

        return Cache::remember("gun_type_data_{$typeSlug}", now()->addHours(PathSeoData::CACHE_TIME), function () use ($typeSlug) {
            $type = RefGunType::where('slug', $typeSlug)->first();

            return $type ? new PostAttributeResource($type) : null;
        });
    }

    public function setAttributes(Post $post, array $parameters)
    {
        foreach ($parameters as $parameter) {
            PostAttribute::firstOrCreate([
                'post_id' => $post->id,
                'attributeable_type' => $parameter['attributeable_type'],
                'attributeable_id' => $parameter['attributeable_id'],
            ]);
        }
    }

    public function getPosts($request, $filters)
    {
        $min_price = $request->include_zero === 'true' ? 1 : $request->min_price;
        $order_by = $request->order ?? 'views';
        $order_type = $request->order_type > 0 ? 'asc' : 'desc';
        $limit = min($request->input('limit', 18), 18);

        $data = IndexPost::filter($filters)
            ->where('archived', false)
            ->whereNotNull('published_at')
            ->city($request->city)
            ->category($request->category)
            ->type($request->type, $request->category)
            ->price($min_price, $request->max_price);

        if ($request->order) {
            if ($request->order === 'date') {
                $order_by = 'created_at';
            }
            $data->orderBy($order_by, $order_type);
        } else {
            $data->orderByDesc('promotion_start_date');
        }

        $posts = $data->paginate($limit);

        $is_empty = $posts->count() === 0;

        if ($is_empty) {

            $city = Cache::get("city_data_{$request->city}");
            $point = $city?->resource?->lat && $city?->resource?->lng ? [$city->resource->lat, $city->resource->lng] : [37.62049, 55.7540584];

            $data = IndexPost::filter($filters)
                ->where('archived', false)
                ->whereNotNull('published_at')
                ->whereGeoDistance('address_geo', '10000km', [$point[0], $point[1]])
                ->category($request->category)
                ->type($request->type, $request->category)
                ->price($min_price, $request->max_price)
                ->orderByDesc('promotion_start_date')
                ->orderBy($order_by, $order_type);

            $posts = $data->paginate($limit);
        }

        return [
            'posts' => $posts,
            'is_empty' => $is_empty,
        ];
    }

    public function getVipPosts($request, $filters)
    {
        $vip = IndexPost::filter($filters)
            ->where('archived', false)
            ->whereNotNull('published_at')
            ->whereNotNull('promotion.vip')
            ->city($request->city)
            ->category($request->category)
            ->type($request->type, $request->category)
            ->orderByDesc('promotion_start_date')
            ->limit(4)
            ->get();

        $is_empty = $vip->count() === 0;

        if ($is_empty) {
            $city = Cache::get("city_data_{$request->city}");
            $point = $city?->resource?->lat && $city?->resource?->lng ? [$city->resource->lat, $city->resource->lng] : [37.62049, 55.7540584];

            $vip = IndexPost::filter($filters)
                ->where('archived', false)
                ->whereNotNull('published_at')
                ->whereNotNull('promotion.vip')
                ->whereGeoDistance('address_geo', '10000km', [$point[0], $point[1]])
                ->category($request->category)
                ->type($request->type, $request->category)
                ->orderByDesc('promotion_start_date')
                ->limit(4)
                ->get();
        }

        return $vip;
    }

    public function getPostsWithVip($request, $filters)
    {
        $min_price = $request->include_zero === 'true' ? 1 : $request->min_price;
        $order_by = $request->order ?? 'views';
        $order_type = $request->order_type > 0 ? 'asc' : 'desc';
        $limit = min($request->input('limit', 18), 18);

        $query = IndexPost::filter($filters)
            ->where('archived', false)
            ->whereNotNull('published_at')
            ->city($request->city)
            ->category($request->category)
            ->type($request->type, $request->category)
            ->price($min_price, $request->max_price);

        if ($request->order) {
            if ($request->order === 'date') {
                $order_by = 'created_at';
            }
            $query->orderBy($order_by, $order_type);
        } else {
            $query->orderByDesc('promotion_start_date')
                ->orderByDesc('created_at');
        }

        $posts = $query->paginate($limit);
        $is_empty = $posts->count() === 0;

        if ($is_empty) {
            $city = Cache::get("city_data_{$request->city}");
            $point = $city?->resource?->lat && $city?->resource?->lng ? [$city->resource->lat, $city->resource->lng] : [37.62049, 55.7540584];

            $query = IndexPost::filter($filters)
                ->where('archived', false)
                ->whereNotNull('published_at')
                ->where(function ($query) use ($point) {
                    $query->whereGeoDistance('address_geo', '10000km', [$point[0], $point[1]])
                        ->orWhereNotNull('registration_geo', '10000km', [$point[0], $point[1]]);
                })

                ->category($request->category)
                ->type($request->type, $request->category)
                ->price($min_price, $request->max_price)
                ->orderByDesc('promotion_start_date')
                ->orderBy($order_by, $order_type);

            $posts = $query->paginate($limit);
        }

        $need_more = $posts->count() < 12;
        $other = [];
        if ($need_more) {
            $moreQuery = IndexPost::filter($filters)
                ->where('archived', false)
                ->whereNotNull('published_at')
                ->whereNotIn('slug', $posts->pluck('slug'))
                ->category($request->category)
                ->type($request->type, $request->category)
                ->price($min_price, $request->max_price)
                ->orderByDesc('promotion_start_date')
                ->orderBy($order_by, $order_type)
                ->limit(10);

            $other = $moreQuery->paginate($limit);
        }

        // Получаем VIP посты из той же выборки
        $vip = $posts->filter(function ($post) {
            return ! empty($post->promotion['vip']);
        })->take(4);

        return [
            'posts' => $posts,
            'other' => $other,
            'is_empty' => $is_empty,
            'vip' => $vip,
        ];
    }
}
