<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TelegramService
{
    /**
     * Отправить сообщение в Telegram напрямую без использования класса нотификации
     *
     * @param  int  $chatId  ID чата Telegram
     * @param  string  $message  Текст сообщения (поддерживает Markdown)
     * @param  array  $buttons  Массив кнопок в формате ['текст' => 'url', ...]
     * @return bool Результат отправки
     */
    public function sendMessage(int $chatId, string $message, array $buttons = []): bool
    {
        try {
            $token = config('services.telegram-bot-api.token');
            $baseUrl = 'https://api.telegram.org/bot'.$token;

            $payload = [
                'chat_id' => $chatId,
                'text' => $message,
                'parse_mode' => 'Markdown',
            ];

            // Добавляем кнопки, если они есть
            if (! empty($buttons)) {
                $inlineKeyboard = [];
                $row = [];
                $count = 0;

                foreach ($buttons as $text => $url) {
                    $row[] = ['text' => $text, 'url' => $url];
                    $count++;

                    // Размещаем по 2 кнопки в ряд
                    if ($count % 2 === 0) {
                        $inlineKeyboard[] = $row;
                        $row = [];
                    }
                }

                // Добавляем оставшиеся кнопки, если есть
                if (! empty($row)) {
                    $inlineKeyboard[] = $row;
                }

                $payload['reply_markup'] = json_encode([
                    'inline_keyboard' => $inlineKeyboard,
                ]);
            }

            $response = Http::post($baseUrl.'/sendMessage', $payload);

            if ($response->successful()) {
                return true;
            }

            // Проверяем, не была ли группа обновлена до супергруппы
            $responseData = $response->json();
            if (isset($responseData['parameters']['migrate_to_chat_id'])) {
                $newChatId = $responseData['parameters']['migrate_to_chat_id'];
                Log::info("Группа обновлена до супергруппы. Попытка отправки с новым ID: {$newChatId}");

                // Обновляем ID сервисного чата в конфигурации, если это сервисный чат
                if ($chatId == config('services.telegram-bot-api.service_chat_id')) {
                    // Здесь можно добавить логику обновления конфигурации или уведомления администратора
                    Log::warning("Необходимо обновить ID сервисного чата в конфигурации на: {$newChatId}");
                }

                // Пробуем отправить сообщение в новый чат
                $payload['chat_id'] = $newChatId;
                $retryResponse = Http::post($baseUrl.'/sendMessage', $payload);

                if ($retryResponse->successful()) {
                    return true;
                }

                Log::error('Ошибка повторной отправки сообщения в Telegram: '.$retryResponse->body());
            } else {
                Log::error('Ошибка отправки сообщения в Telegram: '.$response->body());
            }

            return false;
        } catch (\Exception $e) {
            Log::error('Ошибка отправки сообщения в Telegram: '.$e->getMessage());

            return false;
        }
    }

    /**
     * Отправить файл в Telegram напрямую без использования класса нотификации
     *
     * @param  int  $chatId  ID чата Telegram
     * @param  string  $filePath  Путь к файлу (локальный или URL)
     * @param  string  $caption  Подпись к файлу (поддерживает Markdown)
     * @param  string  $type  Тип файла: photo, document, video, audio, animation
     * @param  array  $buttons  Массив кнопок в формате ['текст' => 'url', ...]
     * @return bool Результат отправки
     */
    public function sendFile(int $chatId, string $filePath, string $caption = '', string $type = 'document', array $buttons = []): bool
    {
        try {
            $token = config('services.telegram-bot-api.token');
            $baseUrl = 'https://api.telegram.org/bot'.$token;

            // Проверяем допустимые типы файлов
            $allowedTypes = ['photo', 'document', 'video', 'audio', 'animation'];
            if (! in_array($type, $allowedTypes)) {
                $type = 'document'; // По умолчанию отправляем как документ
            }

            $endpoint = '/send'.ucfirst($type);

            // Подготавливаем параметры
            $payload = [
                'chat_id' => $chatId,
                'parse_mode' => 'Markdown',
            ];

            if (! empty($caption)) {
                $payload['caption'] = $caption;
            }

            // Добавляем кнопки, если они есть
            if (! empty($buttons)) {
                $inlineKeyboard = [];
                $row = [];
                $count = 0;

                foreach ($buttons as $text => $url) {
                    $row[] = ['text' => $text, 'url' => $url];
                    $count++;

                    // Размещаем по 2 кнопки в ряд
                    if ($count % 2 === 0) {
                        $inlineKeyboard[] = $row;
                        $row = [];
                    }
                }

                // Добавляем оставшиеся кнопки, если есть
                if (! empty($row)) {
                    $inlineKeyboard[] = $row;
                }

                $payload['reply_markup'] = json_encode([
                    'inline_keyboard' => $inlineKeyboard,
                ]);
            }

            // Проверяем, является ли файл URL или локальным файлом
            if (filter_var($filePath, FILTER_VALIDATE_URL)) {
                // Если это URL, отправляем ссылку на файл
                $fileParam = $type === 'photo' ? 'photo' : $type;
                $payload[$fileParam] = $filePath;

                $response = Http::post($baseUrl.$endpoint, $payload);
            } else {
                // Если это локальный файл, отправляем его с помощью multipart/form-data
                $fileParam = $type === 'photo' ? 'photo' : $type;

                $response = Http::attach(
                    $fileParam,
                    file_get_contents($filePath),
                    basename($filePath)
                )->post($baseUrl.$endpoint, $payload);
            }

            if ($response->successful()) {
                return true;
            }

            // Проверяем, не была ли группа обновлена до супергруппы
            $responseData = $response->json();
            if (isset($responseData['parameters']['migrate_to_chat_id'])) {
                $newChatId = $responseData['parameters']['migrate_to_chat_id'];
                Log::info("Группа обновлена до супергруппы. Попытка отправки файла с новым ID: {$newChatId}");

                // Обновляем ID сервисного чата в конфигурации, если это сервисный чат
                if ($chatId == config('services.telegram-bot-api.service_chat_id')) {
                    Log::warning("Необходимо обновить ID сервисного чата в конфигурации на: {$newChatId}");
                }

                // Пробуем отправить файл в новый чат
                $payload['chat_id'] = $newChatId;

                // Повторяем отправку файла с новым ID чата
                if (filter_var($filePath, FILTER_VALIDATE_URL)) {
                    $fileParam = $type === 'photo' ? 'photo' : $type;
                    $payload[$fileParam] = $filePath;
                    $retryResponse = Http::post($baseUrl.$endpoint, $payload);
                } else {
                    $fileParam = $type === 'photo' ? 'photo' : $type;
                    $retryResponse = Http::attach(
                        $fileParam,
                        file_get_contents($filePath),
                        basename($filePath)
                    )->post($baseUrl.$endpoint, $payload);
                }

                if ($retryResponse->successful()) {
                    return true;
                }

                Log::error('Ошибка повторной отправки файла в Telegram: '.$retryResponse->body());
            } else {
                Log::error('Ошибка отправки файла в Telegram: '.$response->body());
            }

            return false;
        } catch (\Exception $e) {
            Log::error('Ошибка отправки файла в Telegram: '.$e->getMessage());

            return false;
        }
    }
}
