<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TextRuCheck extends Model
{
    protected $fillable = [
        'checkable_type',
        'checkable_id',
        'text_ru_uid',
        'result',
        'spell_check',
        'seo_check',
        'text_unique',
        'status',
        'error_code',
        'error_desc',
        'date_check',
    ];

    protected $casts = [
        'result' => 'array',
        'spell_check' => 'array',
        'seo_check' => 'array',
    ];

    public function checkable()
    {
        return $this->morphTo();
    }
}
