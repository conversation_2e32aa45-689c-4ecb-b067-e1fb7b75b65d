<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RefCity extends Model
{
    protected $fillable = [
        'name',
        'name_pred',
        'slug',
        'order',
        'region',
        'ref_country_id',
        'region_number',
    ];

    public function country(): BelongsTo
    {
        return $this->belongsTo(RefCountry::class, 'ref_country_id');
    }

    public function posts(): HasMany
    {
        return $this->hasMany(Post::class, 'ref_city_id');
    }
}
