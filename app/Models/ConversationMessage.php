<?php

namespace App\Models;

use App\Events\ConversationMessageCreated;
use App\Traits\ImagesConversionTrait;
use Cmgmyr\Messenger\Models\Message;

class ConversationMessage extends Message
{
    use ImagesConversionTrait;

    protected $fillable = [
        'thread_id',
        'user_id',
        'body',
    ];

    protected $dispatchesEvents = [
        'created' => ConversationMessageCreated::class,
    ];
}
