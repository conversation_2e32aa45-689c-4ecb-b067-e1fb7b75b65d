<?php

namespace App\Models;

use Elegantly\Media\Concerns\HasMedia;
use Elegantly\Media\Definitions\MediaConversionImage;
use Elegantly\Media\MediaCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Spatie\Image\Enums\Fit;

class News extends Model
{
    use HasFactory;
    use HasMedia;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'content',
        'image',
        'image_alt',
        'published_at',
        'status',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    protected $casts = [
        'published_at' => 'datetime',
    ];

    public function registerMediaCollections(): array
    {
        return [
            new MediaCollection(
                name: 'image',
                acceptedMimeTypes: [ // (optional) Specify accepted file types
                    'image/jpeg',
                    'image/png',
                    'image/webp',
                ], // If true, only the latest file will be kept
                single: true, // (optional) Specify where the file will be stored
                conversions: [
                    new MediaConversionImage(
                        name: '350',
                        queued: true,
                        width: 350, // Conversion will not be generated at upload time
                    ),
                    new MediaConversionImage(
                        name: '50',
                        queued: true,
                        width: 50,
                        height: 50,
                        fit: Fit::Crop,
                    ),
                ]
            ),
            new MediaCollection(
                name: 'images',
                acceptedMimeTypes: [ // (optional) Specify accepted file types
                    'image/jpeg',
                    'image/png',
                    'image/webp',
                ], // If true, only the latest file will be kept
                conversions: [
                    new MediaConversionImage(
                        name: '350',
                        queued: false, // Conversion will not be generated at upload time
                        width: 350
                    ),
                ]
            ),
        ];
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($news) {
            $news->slug = Str::slug($news->title);
        });

        static::updating(function ($news) {
            if ($news->isDirty('title')) {
                $news->slug = Str::slug($news->title);
            }
        });
    }

    public function textRuChecks()
    {
        return $this->morphOne(TextRuCheck::class, 'checkable');
    }
}
