<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserTelegram extends Model
{
    protected $fillable = [
        'user_id',
        'start_token',
        'chat_id',
        'username',
        'first_name',
        'last_name',
        'language_code',
        'is_premium',
    ];

    protected $casts = [
        'is_premium' => 'boolean',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
