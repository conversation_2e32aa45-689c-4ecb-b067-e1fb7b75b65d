<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    const ALL_FILTERS = [
        'anies' => [
            'label' => 'Дополнительно',
            'placeholder' => '',
            'values' => RefAny::class,
        ],
        'gun_types' => [
            'label' => 'Тип ствола',
            'placeholder' => 'Например: Гладкоствольное',
            'values' => RefGunType::class,
        ],
        'types' => [
            'label' => 'Подраздел',
            'placeholder' => 'Например: Гладкоствольное',
            'values' => RefType::class,
        ],
        'calibers' => [
            'label' => 'Калибр',
            'placeholder' => 'Например: 12 калибр',
            'values' => RefCaliber::class,
        ],
        'gun_orientations' => [
            'label' => 'Положение стволов',
            'placeholder' => 'Например: Горизонтальное',
            'values' => RefGunOrientation::class,
        ],
        'gun_reloadings' => [
            'label' => 'Система перезарядки',
            'placeholder' => 'Например: Переломное',
            'values' => RefGunReloading::class,
        ],
        'conditions' => [
            'label' => 'Состояние',
            'placeholder' => 'Например: Хорошее',
            'values' => RefCondition::class,
        ],
        'brands' => [
            'label' => 'Производитель',
            'placeholder' => 'Например: Remington',
            'values' => RefBrand::class,
        ],
    ];

    const FILTER_CONFIG = [
        'hunting' => [
            //            'types' => ['is_filter' => true],
            'gun_types' => ['is_filter' => true, 'required' => true],
            'calibers' => ['is_filter' => true],
            'gun_orientations' => ['required' => true],
            'gun_reloadings' => ['is_filter' => true],
            'conditions' => ['required' => true],
        ],
        'reloading' => [
            'types' => ['is_filter' => true],
            'calibers' => ['is_filter' => false],
            'conditions' => ['is_filter' => true],
        ],
        'zip' => [
            'types' => ['is_filter' => true],
            'conditions' => ['is_filter' => true],
            'brands' => ['is_filter' => false],
        ],
        'optika_priceli' => [
            'types' => ['is_filter' => true],
            'conditions' => ['is_filter' => true],
            'brands' => ['is_filter' => false],
        ],
        null => [
            'conditions' => ['is_filter' => true],
            'types' => ['is_filter' => true],
        ],
    ];

    public static function getFiltersFor(?string $category, bool $is_filter = false): array
    {
        $filters = self::FILTER_CONFIG[$category] ?? self::FILTER_CONFIG[null];

        return collect($filters)->mapWithKeys(function ($config, $key) use ($is_filter) {
            $is_filter_value = $config['is_filter'] ?? false;
            if ($is_filter && ! $is_filter_value) {
                return [];
            }
            $base = self::ALL_FILTERS[$key] ?? [];

            return [$key => array_merge($base, $config)];
        })->toArray();
    }

    protected $fillable = [
        'name',
        'slug',
        'icon',
        'registration',
        'parent_id',
    ];
}
