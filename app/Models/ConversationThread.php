<?php

namespace App\Models;

use Cmgmyr\Messenger\Models\Message;
use Cmgmyr\Messenger\Models\Thread;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class ConversationThread extends Thread
{
    protected $fillable = [
        'subject',
        'post_id',
        'description',
        'source_id',
        'source_type',
    ];

    public function source(): MorphTo
    {
        return $this->morphTo();
    }

    public function latest(): HasOne
    {
        return $this->hasOne(Message::class, 'thread_id')->latestOfMany();
    }
}
