<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RefModeration extends Model
{
    const IS_NOT_APPROVED = 0;

    const IS_APPROVED = 1;

    const IS_BANNED = 2;

    const MODERATION = [
        self::IS_NOT_APPROVED => 'not_approved',
        self::IS_APPROVED => 'approved',
        self::IS_BANNED => 'banned',
    ];

    protected $fillable = [
        'type',
    ];
}
