<?php

namespace App\Models;

use App\Events\ImportPostQueueCreated;
use Illuminate\Database\Eloquent\Model;

class ImportPostQueue extends Model
{
    protected $fillable = [
        'source_name',
        'source',
        'user_name',
        'user_phone',
        'user_avatar',
        'status',
        'process',
        'failed_at',
        'error_message',
        'attempts',
    ];

    protected $casts = [
        'failed_at' => 'datetime',
    ];

    protected static function booted(): void
    {
        static::created(function (ImportPostQueue $importPostQueue) {
            ImportPostQueueCreated::dispatch($importPostQueue);
        });
    }
}
