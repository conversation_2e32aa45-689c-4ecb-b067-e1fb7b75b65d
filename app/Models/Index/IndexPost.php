<?php

namespace App\Models\Index;

use App\Models\Scopes\ApprovedScope;
use <PERSON><PERSON>enG\Explorer\Application\Explored;
use <PERSON><PERSON>enG\Explorer\Application\IndexSettings;
use <PERSON><PERSON>\Scout\Searchable;
use PDPhilip\Elasticsearch\Eloquent\Model;
use PDPhil<PERSON>\Elasticsearch\Query\Builder;

class IndexPost extends Model implements Explored, IndexSettings
{
    use Searchable;

    protected $connection = 'elasticsearch';

    protected $index = 'gunpost_posts';

    protected $guarded = [];

    protected static function booted(): void
    {
        static::addGlobalScope(new ApprovedScope);
    }

    public function mappableAs(): array
    {
        return [
            'slug' => 'keyword',
            'city_slug' => 'keyword',
            'category' => 'keyword',
            'description' => [
                'type' => 'text',
                'analyzer' => 'edge_text_analyzer',
                'search_analyzer' => 'standard_lowercase',
            ],
            'title' => [
                'type' => 'text',
                'analyzer' => 'edge_text_analyzer',
                'search_analyzer' => 'standard_lowercase',
            ],
            'published_at' => 'date',
            'promotion_start_date' => 'date',
        ];
    }

    public function indexSettings(): array
    {
        return [
            'analysis' => [
                'filter' => [
                    'edge_ngram_filter' => [
                        'type' => 'edge_ngram',
                        'min_gram' => 2,
                        'max_gram' => 20,
                    ],
                ],
                'analyzer' => [
                    'standard_lowercase' => [
                        'type' => 'custom',
                        'tokenizer' => 'standard',
                        'filter' => ['lowercase', 'asciifolding'],
                    ],
                    'edge_text_analyzer' => [
                        'type' => 'custom',
                        'tokenizer' => 'standard',
                        'filter' => ['lowercase', 'asciifolding', 'edge_ngram_filter'],
                    ],
                ],
            ],
        ];
    }

    public function scopeFilter($query, array $filters): void
    {
        foreach ($filters as $type => $slug) {
            $query->whereNestedObject('attributes', function (Builder $query) use ($type, $slug) {
                $query->where('type', $type);
                $query->where('slug', $slug);
            });
        }
    }

    public function scopeCity($query, ?string $slug): void
    {
        if (! empty($slug)) {
            $query->where('city_slug', $slug);
        }
    }

    public function scopeCategory($query, ?string $slug): void
    {
        if (! empty($slug)) {
            $query->where('category', $slug);
        }
    }

    public function scopeType($query, ?string $slug, ?string $category): void
    {
        if (! empty($slug)) {
            $query->where('type', $slug);
            $query->where('category', $category);
        }
    }

    public function scopePrice($query, ?int $min_price, ?int $max_price): void
    {
        if ($min_price !== null) {
            $query->where('price', '>=', $min_price);
        }

        if ($max_price !== null) {
            $query->where('price', '<=', $max_price);
        }
    }
}
