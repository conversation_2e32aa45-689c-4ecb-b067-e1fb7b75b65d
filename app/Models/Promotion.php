<?php

namespace App\Models;

use App\Models\Index\IndexPost;
use Bavix\Wallet\Interfaces\Customer;
use Bavix\Wallet\Interfaces\ProductInterface;
use Bavix\Wallet\Traits\HasWallet;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Promotion extends Model implements ProductInterface
{
    use HasWallet;
    use SoftDeletes;

    protected $fillable = [
        'post_id',
        'promotion_type_id',
        'price',
        'duration',
        'created_at',
        'purchased_at',
        'processed_at',
        'expires_at',
    ];

    protected $casts = [
        'purchased_at' => 'datetime',
        'processed_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    public function getAmountProduct(Customer $customer): int|string
    {
        return $this->price;
    }

    public function getMetaProduct(): ?array
    {
        return [
            'id' => $this->id,
            'title' => $this->promotionType->name,
            'price' => $this->price,
            'duration' => $this->duration,
        ];
    }

    public function post(): BelongsTo
    {
        return $this->belongsTo(Post::class);
    }

    public function indexPost(): BelongsTo
    {
        return $this->belongsTo(IndexPost::class);
    }

    public function promotionType(): BelongsTo
    {
        return $this->belongsTo(PromotionType::class);
    }
}
