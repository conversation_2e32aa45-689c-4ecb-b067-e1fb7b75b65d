<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Производитель, например:
 * Remington Arms США	Северная Америка
 */
class RefVendor extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'region',
        'ref_country_id',
    ];

    public function country(): BelongsTo
    {
        return $this->belongsTo(RefCountry::class, 'ref_country_id');
    }
}
