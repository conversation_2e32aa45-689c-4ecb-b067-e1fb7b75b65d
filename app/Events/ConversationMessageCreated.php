<?php

namespace App\Events;

use App\Http\Resources\ConversationMessageResource;
use App\Models\ConversationMessage;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Vinkla\Hashids\Facades\Hashids;

class ConversationMessageCreated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ConversationMessageResource $message;

    /**
     * Create a new event instance.
     */
    public function __construct(ConversationMessage $message)
    {
        $message->load(['user', 'media']);
        $this->message = new ConversationMessageResource($message);
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'message.created';
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, Channel>
     */
    public function broadcastOn(): array
    {
        $id = Hashids::encode($this->message->thread_id);

        return [
            new PrivateChannel("chat.$id"),
        ];
    }
}
