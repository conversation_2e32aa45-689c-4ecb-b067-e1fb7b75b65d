<?php

namespace App\Console\Commands;

use App\Jobs\ProcessImportPostQueueJob;
use App\Models\ImportPostQueue;
use Illuminate\Console\Command;

class ProcessImportPostsQueueJobCommand extends Command
{
    protected $signature = 'import:gunsbroker:manage
                            {action : Действие: process, retry, cleanup}
                            {--status= : Обработать только записи с определенным статусом (для process)}
                            {--max-attempts=3 : Максимальное количество попыток (для retry)}
                            {--days=7 : Количество дней для хранения завершенных записей (для cleanup)}';

    protected $description = 'Управление очередью импорта объявлений Gunsbroker: обработка, повтор неудачных, очистка';

    public function handle(): void
    {
        $action = $this->argument('action');

        match ($action) {
            'process' => $this->processQueue(),
            'retry' => $this->retryFailed(),
            'cleanup' => $this->cleanupCompleted(),
            default => $this->error("Неизвестное действие: {$action}. Доступные: process, retry, cleanup")
        };
    }

    private function processQueue(): void
    {
        $query = ImportPostQueue::where('source_name', 'gunsbroker');

        if ($status = $this->option('status')) {
            $query->where('status', $status);
        } else {
            // По умолчанию обрабатываем все кроме завершенных
            $query->whereNotIn('status', ['completed']);
        }

        $posts = $query->get();

        if ($posts->isEmpty()) {
            $this->info('Нет записей для обработки');
            return;
        }

        $this->info("Найдено {$posts->count()} записей для обработки");

        $bar = $this->output->createProgressBar($posts->count());
        $bar->start();

        foreach ($posts as $post) {
            ProcessImportPostQueueJob::dispatch($post);
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info("Запущено {$posts->count()} джоб для обработки");
    }

    private function retryFailed(): void
    {
        $maxAttempts = (int) $this->option('max-attempts');

        $failedPosts = ImportPostQueue::where('source_name', 'gunsbroker')
            ->whereNotNull('failed_at')
            ->where('attempts', '<', $maxAttempts)
            ->whereNotIn('status', ['completed'])
            ->get();

        if ($failedPosts->isEmpty()) {
            $this->info('Нет неудачных записей для повторной обработки');
            return;
        }

        $this->info("Найдено {$failedPosts->count()} неудачных записей для повторной обработки");

        $bar = $this->output->createProgressBar($failedPosts->count());
        $bar->start();

        foreach ($failedPosts as $post) {
            // Сбрасываем информацию об ошибке
            $post->update([
                'failed_at' => null,
                'error_message' => null
            ]);

            ProcessImportPostQueueJob::dispatch($post);
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info("Запущено {$failedPosts->count()} джоб для повторной обработки");
    }

    private function cleanupCompleted(): void
    {
        $days = (int) $this->option('days');

        $cutoffDate = now()->subDays($days);

        $deletedCount = ImportPostQueue::where('source_name', 'gunsbroker')
            ->where('status', 'completed')
            ->where('updated_at', '<', $cutoffDate)
            ->delete();

        $this->info("Удалено {$deletedCount} завершенных записей старше {$days} дней");
    }
}
