<?php

namespace App\Console\Commands;

use App\Events\PostChangeEvent;
use App\Models\Category;
use App\Models\Post;
use App\Models\PostAttribute;
use App\Models\RefAny;
use App\Models\RefBrand;
use App\Models\RefCondition;
use App\Models\RefModeration;
use App\Models\RefType;
use App\Models\User;
use Drnxloc\LaravelHtmlDom\HtmlDomParser;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class ParseLineFCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'parse:linef';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Парсинг официального фида Line-F.ru';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $response = Http::withOptions(['verify' => false])
            ->get('https://line-f.ru/i/feed.yml');
        $xmlString = $response->body();

        $categoryMap = [
            // Примеры
            1 => 'zip',      // Приклады → ЗИП
            3 => 'zip',      // Дульные устройства → ЗИП
            4 => 'zip',
            5 => 'zip',
            6 => 'zip',
            7 => 'zip',
            9 => 'zip',
            10 => 'zip',
            11 => 'zip',
            13 => 'zip',
            14 => 'zip',
            15 => 'zip',
            16 => 'zip',
            17 => 'optika_priceli', // Кронштейны и целики → Оптика
            18 => 'optika_priceli', // Коллиматоры
            21 => 'optika_priceli', // Оптические прицелы
            22 => 'zip',
            23 => 'airsoft',
            24 => 'airsoft',
            25 => 'airsoft',
            27 => 'kobury',
            28 => 'kobury',
            29 => 'kobury',
            31 => 'kobury',
            32 => 'kobury',
            33 => 'kobury',
            34 => 'kobury',
            35 => 'kobury',
            36 => 'reloading',
            42 => 'reloading',
            43 => 'reloading',
            46 => 'kobury',
            47 => 'kobury',
            49 => 'zip',
            50 => 'zip',
            51 => 'reloading',
            52 => 'reloading',
            53 => 'reloading',
            55 => 'kobury',
            56 => 'airsoft',
            57 => 'airsoft',
            58 => 'airsoft',
            59 => 'airsoft',
            60 => 'airsoft',
        ];

        // Преобразуем XML-строку в объект
        $xml = simplexml_load_string($xmlString);

        $categories = collect($xml->xpath('//category'))->map(function ($category) {

            $id = (int) $category['id'];
            $name = (string) $category;

            $type = RefType::where('name', $name)->with('category')->first();

            if ($type && $type?->category?->slug === $type?->slug) {
                $type_slug = null;
                $type_name = null;
            } elseif ($type) {
                $type_slug = $type->slug;
                $type_name = $type->name;
            }

            if ($type) {
                return [
                    'id' => (string) $id,
                    'name' => (string) $category,
                    'type_name' => $type_name,
                    'type_slug' => $type_slug,
                    'category' => $type->category->slug,
                ];
            } else {
                return [];
            }
        });

        $seller = User::firstOrCreate([
            'name' => 'Линия Огня',
            'is_shop' => true,
            'phone' => '+78006005578',
        ]);

        $offers = collect($xml->xpath('//offer'));

        $this->output->progressStart($offers->count());

        $offers->each(function ($offer) use ($categories, $seller) {
            try {
                $this->parseOffer($offer, $categories, $seller);
            } catch (\Throwable $e) {
                $this->error($e->getMessage());
            }
            $this->output->progressAdvance();
        });

        $this->output->progressFinish();
    }

    private function parseOffer($offer, $categories, $seller)
    {
        $post = Post::where('source', $offer->url)->first();
        $available = (bool) $offer['available'];

        if (! $available && $post) {
            $post->published_at = null;
            $post->save();

            PostChangeEvent::dispatch($post);

            //            $this->warn('Post not available ' . $offer->url);
            return;
        }

        $category_map = $categories->where('id', $offer->categoryId)->first();

        if (! isset($category_map['category'])) {
            return;
        }

        $category = Category::where('slug', $category_map['category'])->first();
        $type = RefType::where('slug', $category_map['type_slug'])->first();

        if ($category) {
            $post = Post::firstOrNew([
                'source' => $offer->url,
            ]);
            $name = html_entity_decode($offer->name);

            // has name Подарочный сертификат skip
            if (str_contains(Str::lower($name), 'подарочный')) {
                return;
            }

            $post->title = $name;
            $post->description = $name;
            $post->category_id = $category->id;
            $post->price = (int) $offer->price;
            $post->moderation_id = RefModeration::IS_APPROVED;
            $post->published_at = now();
            $post->user_id = $seller->id;
            $post->ref_city_id = 3;
            $post->address = 'Санкт-Петербург, Средний пр. В.О., 85, лит. У, пом. 95Н';
            $post->address_geo = [59.936458, 30.254262];
            $post->can_ship = $offer->delivery ?? false;

            $post->save();

            $pageData = $this->parsePage($post);
            $post->description = $pageData['description'];

            $post->slug = $post->id.'-'.Str::slug($post->title);
            $post->save();

            $this->downloadPhotos($post, $pageData['photos']);

            $brand = RefBrand::where('name', $offer->vendor)->first();

            PostAttribute::where('post_id', $post->id)->delete();

            if ($brand) {
                PostAttribute::firstOrCreate([
                    'post_id' => $post->id,
                    'attributeable_type' => RefBrand::class,
                    'attributeable_id' => $brand->id,
                ]);
            }

            if ($type) {
                PostAttribute::firstOrCreate([
                    'post_id' => $post->id,
                    'attributeable_type' => RefType::class,
                    'attributeable_id' => $type->id,
                ]);
            }

            foreach ($pageData['attributes'] as $attr_name => $attr_value) {
                try {
                    $ref_any = RefAny::firstOrCreate([
                        'name' => $attr_name,
                        'slug' => Str::slug($attr_name.'-'.$attr_value),
                        'value' => $attr_value,
                    ]);

                    PostAttribute::firstOrCreate([
                        'post_id' => $post->id,
                        'attributeable_type' => RefAny::class,
                        'attributeable_id' => $ref_any->id,
                    ]);
                } catch (\Throwable $e) {
                    continue;
                }
            }

            PostAttribute::firstOrCreate([
                'post_id' => $post->id,
                'attributeable_type' => RefCondition::class,
                'attributeable_id' => RefCondition::where('name', 'Новое')->first()->id,
            ]);

            PostChangeEvent::dispatch($post);
            //            $this->info($post->title);
            //            $this->line('http://localhost:3000/'.$post->category->slug.'/'.$post->slug.'.html');
        }
    }

    private function downloadPhotos(Post $post, array $photoUrls): void
    {
        foreach ($photoUrls as $photo) {
            $filename = md5($photo);
            $medias = $post->getMedia('images');

            $has = $medias->where('name', $filename)->first();

            if (! $has?->path) {
                //                $this->line("Photo: $photo");
                $response = Http::withOptions(['verify' => false])->get($photo);

                $tempPath = tempnam(sys_get_temp_dir(), 'images');
                file_put_contents($tempPath, $response->body());

                $post->addMedia(
                    file: $tempPath,
                    collectionName: 'images',
                    name: $filename
                );

                unlink($tempPath);
            }
        }
    }

    private function parsePage(Post $post)
    {
        $response = Http::withOptions([
            'verify' => false,
        ])->get($post->source);

        $dom = HtmlDomParser::str_get_html($response->body());
        $content = $dom->find('#product-description', 0)->innertext;
        $photos = $dom->find('.product-images .slider__slide img');
        $photoUrls = [];
        foreach ($photos as $photo) {
            $photoUrls[] = 'https://line-f.ru'.$photo->src;
        }

        $attributes = [];
        $allowedAttributes = [
            'Вид',
            'Диаметр трубки',
            'Задняя точка',
            'Калибр',
            'Крепление',
            'Крепление ремня к антабке',
            'Материал',
            'Обхват талии',
            'Передняя точка',
            'Подсветка',
            'Размер',
            'Резьба',
            'Система крепления',
            'Совместимость',
            'Тип',
            'Увеличение',
            'Усилие спуска (кг)',
            'Цвет',
            'Цвет линз',
        ];

        $dt = $dom->find('.options-list dt');
        $dd = $dom->find('.options-list dd');

        foreach ($dt as $index => $item) {
            $key = trim(strip_tags($item->innertext));
            $key = str_replace(':', '', $key);
            $value = trim(strip_tags($dd[$index]->innertext));

            if (in_array($key, $allowedAttributes)) {
                $attributes[$key] = $value;
            }
        }

        return [
            'description' => $this->cleanHtmlPreserveNewlines($content),
            'photos' => $photoUrls,
            'attributes' => $attributes,
        ];
    }

    private function cleanHtmlPreserveNewlines($html)
    {
        // Декодируем HTML-сущности (например, &nbsp;)
        $html = html_entity_decode($html);

        // Заменяем переносные теги и <li> на перенос строки
        $html = preg_replace('/<\s*(br|\/?p|\/?ul|li)\s*\/?>/i', "\n", $html);

        // Удаляем все оставшиеся HTML-теги
        $text = strip_tags($html);

        // Заменяем неразрывный пробел на обычный
        $text = str_replace("\u{A0}", ' ', $text);

        // Приводим переносы к Unix-формату
        $text = preg_replace("/\r\n|\r/", "\n", $text);

        // Убираем пробелы вокруг переносов
        $text = preg_replace("/ *\n+ */", "\n", $text);

        // Сжимаем подряд идущие пробелы
        $text = preg_replace('/[ \t]+/', ' ', $text);

        // Максимум два переноса подряд
        $text = preg_replace("/\n{3,}/", "\n\n", $text);

        return trim($text);
    }
}
