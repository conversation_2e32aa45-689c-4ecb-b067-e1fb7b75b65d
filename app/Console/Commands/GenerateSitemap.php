<?php

namespace App\Console\Commands;

use App\Services\SitemapService;
use Illuminate\Console\Command;

class GenerateSitemap extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sitemap:generate
                            {--all : Generate all sitemaps}
                            {--ads : Generate ads sitemap}
                            {--categories : Generate categories sitemap}
                            {--categories-types : Generate categories types sitemap}
                            {--geo-categories : Generate geo-categories sitemap}
                            {--geo-cat-types : Generate geo-categories-types sitemap}
                            {--images : Generate images sitemap}
                            {--news : Generate news sitemap}
                            {--index : Generate sitemap index}
                            {--ping : Ping search engines}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate sitemap files and upload them to S3';

    /**
     * Execute the console command.
     */
    public function handle(SitemapService $sitemapService)
    {
        $this->info('Starting sitemap generation...');

        $options = $this->options();
        $generateAll = $options['all'] || (! $options['ads'] && ! $options['categories'] && ! $options['categories-types'] &&
                                          ! $options['geo-categories'] && ! $options['geo-cat-types'] &&
                                          ! $options['images'] && ! $options['news'] && ! $options['index'] && ! $options['ping']);

        if ($generateAll) {
            $this->info('Generating all sitemaps...');
            $sitemapService->generateAll();
        } else {
            if ($options['ads']) {
                $this->info('Generating ads sitemap...');
                $sitemapService->generateAdsSitemap();
            }

            if ($options['categories']) {
                $this->info('Generating categories sitemap...');
                $sitemapService->generateCategoriesSitemap();
            }

            if ($options['categories-types']) {
                $this->info('Generating categories types sitemap...');
                $sitemapService->generateCategoriesTypesSitemap();
            }

            if ($options['geo-categories']) {
                $this->info('Generating geo-categories sitemap...');
                $sitemapService->generateGeoCategoriesSitemap();
            }

            if ($options['geo-cat-types']) {
                $this->info('Generating geo-categories-types sitemap...');
                $sitemapService->generateGeoCategoriesTypesSitemap();
            }

            if ($options['images']) {
                $this->info('Generating images sitemap...');
                $sitemapService->generateImagesSitemap();
            }

            if ($options['news']) {
                $this->info('Generating news sitemap...');
                $sitemapService->generateNewsSitemap();
            }

            if ($options['index']) {
                $this->info('Generating sitemap index...');
                $sitemapService->generateSitemapIndex();
            }

            if ($options['ping']) {
                $this->info('Pinging search engines...');
                $sitemapService->pingSearchEngines();
            }
        }

        $this->info('Sitemap generation completed successfully!');

        return Command::SUCCESS;
    }
}
