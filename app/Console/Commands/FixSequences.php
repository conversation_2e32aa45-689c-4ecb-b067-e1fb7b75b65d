<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class FixSequences extends Command
{
    protected $signature = 'db:fix-sequences {--schema=public}';

    protected $description = 'Normalize PostgreSQL sequences (fix increments) for all tables';

    public function handle()
    {
        $schema = $this->option('schema');

        // Получаем все таблицы из схемы
        $tables = DB::table('information_schema.tables')
            ->where('table_schema', $schema)
            ->where('table_type', 'BASE TABLE')
            ->pluck('table_name');

        foreach ($tables as $table) {
            // Пропускаем, если нет колонки id
            if (! Schema::hasColumn($table, 'id')) {
                continue;
            }

            // Определяем реальное имя последовательности для колонки id (serial/identity)
            $qualifiedTable = sprintf('"%s"."%s"', $schema, $table);

            try {
                $seqRow = DB::selectOne(
                    "SELECT pg_get_serial_sequence(?, 'id') AS seq",
                    [$qualifiedTable]
                );

                // Если последовательности нет (например, id строковый/uuid) — пропускаем
                if (! $seqRow || ! $seqRow->seq) {
                    $this->line("Skip {$qualifiedTable}: no owned sequence for id");

                    continue;
                }

                $sequence = $seqRow->seq; // вида: schema.table_id_seq

                // Получаем максимальный id
                $maxId = DB::table(DB::raw($qualifiedTable))->max('id');
                $maxId = is_null($maxId) ? 0 : (int) $maxId;

                $this->line("Set sequence {$sequence} to {$maxId} for {$qualifiedTable}");

                // Устанавливаем значение последовательности так, чтобы следующий nextval дал maxId+1
                // Для пустых таблиц: установить так, чтобы следующий nextval() вернул 1
                if ($maxId === 0) {
                    DB::statement('SELECT setval(?::regclass, ?, ?)', [$sequence, 1, false]);
                } else {
                    // Для непустых: следующий nextval() вернет maxId+1
                    DB::statement('SELECT setval(?::regclass, ?, ?)', [$sequence, $maxId, true]);
                }
            } catch (\Throwable $e) {
                $this->error("Error fixing {$qualifiedTable}: ".$e->getMessage());
            }
        }

        $this->info('✅ Sequences normalized.');

        return Command::SUCCESS;
    }
}
