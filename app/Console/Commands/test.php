<?php

namespace App\Console\Commands;

use App\Models\Index\IndexPost;
use Illuminate\Console\Command;

class test extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $post = IndexPost::where('slug', '12-kobura-falco-mod4902-poiasnaia-dlia-p226')->first();
    }
}
