<?php

namespace App\Console\Commands;

use App\Models\Index\IndexViews;
use App\Models\Post;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class IndexPostViewsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'index:views';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Перенос просмотров из Redis';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $cursor = null;
        $prefix = config('database.redis.options.prefix');
        $pattern = $prefix.'views:*';
        $chunkSize = 1000;
        $result = [];
        $viewsByPostSlug = [];

        do {
            [$cursor, $keys] = Redis::scan($cursor, ['MATCH' => $pattern, 'COUNT' => $chunkSize]);

            if (! empty($keys)) {
                $func = function (string $key) use ($prefix): string {
                    return str_replace($prefix, '', $key);
                };
                $keysWithoutPrefix = array_map($func, $keys);
                $values = Redis::mget($keysWithoutPrefix);

                foreach ($keys as $index => $key) {
                    $parts = explode(';', substr($key, strlen($prefix.'views:')));

                    $postSlug = $parts[0] ?? null;
                    $views = (int) ($values[$index] ?? 0);

                    if (! $postSlug) {
                        Log::warning("Redis key без post_slug: {$key}");

                        continue;
                    }

                    $viewsByPostSlug[$postSlug] = ($viewsByPostSlug[$postSlug] ?? 0) + $views;

                    $result[] = array_filter([
                        'post_slug' => $postSlug,
                        'fingerprint' => $parts[1] ?? null,
                        'user_id' => $parts[2] ?? null,
                        'views' => $views,
                        'created_at' => Carbon::now(),
                    ]);
                }

                Redis::pipeline(function ($pipe) use ($keysWithoutPrefix) {
                    foreach (array_chunk($keysWithoutPrefix, 500) as $chunk) {
                        $pipe->unlink(...$chunk);
                    }
                });
            }
        } while ($cursor !== null);

        // Запись в IndexViews
        IndexViews::withoutRefresh()->insert($result);

        // Массовое обновление views в Post
        // TODO increment не вызывает обновление модели, как тогда массово обновить PostIndex?

        //        foreach ($viewsByPostSlug as $slug => $views) {
        //            Post::where('slug', $slug)->increment('views', $views);
        //        }

        foreach ($viewsByPostSlug as $slug => $views) {
            DB::transaction(function () use ($slug, $views) {
                // Получаем пост с блокировкой
                $post = Post::where('slug', $slug)->lockForUpdate()->first();

                if ($post) {
                    $post->views += $views;
                    $post->save();
                }
            });
        }
    }
}
