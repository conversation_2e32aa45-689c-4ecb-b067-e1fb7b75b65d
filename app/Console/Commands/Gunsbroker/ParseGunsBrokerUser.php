<?php

namespace App\Console\Commands\Gunsbroker;

use App\Models\Index\IndexGunsBrokerUser;
use App\Services\Parse\GunsbrokerService;
use Carbon\Carbon;
use Drnxloc\LaravelHtmlDom\HtmlDomParser;
use Illuminate\Console\Command;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Pool;
use Illuminate\Support\Facades\Http;

class ParseGunsBrokerUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'parse:gunsbroker:user {latest_id? : Optional starting user_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Парсинг всех пользователей GunsBroker по id';

    public $cookieJar;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Allow overriding the starting ID via command argument
        $providedId = $this->argument('latest_id');
        if ($providedId) {
            $latest_id = (int) $providedId;
        } else {
            $latestUser = IndexGunsBrokerUser::latest()->first();
            $latest_id = $latestUser?->user_id ?? 1;
        }

        $consecutive404 = 0;
        $maxConsecutive404 = 2000;

        $batchSize = 10;
        $currentId = $latest_id;

        while (true) {
            $ids = range($currentId, $currentId + $batchSize - 1);
            $responses = Http::withOptions([
                'proxy' => config('services.proxy.url'),
                'verify' => false,
            ])->pool(function (Pool $pool) use ($ids) {
                $requests = [];
                foreach ($ids as $id) {
                    $requests[$id] = $pool->get("https://gunsbroker.ru/users/{$id}");
                }

                return $requests;
            });

            foreach ($responses as $index => $response) {
                $id = $ids[$index];
                try {
                    $success = $this->parseUserResponse($id, $response);
                } catch (\Exception $e) {
                    $this->error($e->getMessage());

                    continue;
                }

                if (! $success) {
                    $consecutive404++;
                    $this->warn("User id {$id} returned 404 ({$consecutive404} consecutive)");
                    if ($consecutive404 >= $maxConsecutive404) {
                        $this->info("Reached {$maxConsecutive404} consecutive 404 responses. Stopping.");
                        break 2;
                    }
                } else {
                    $consecutive404 = 0;
                }
            }

            $currentId += $batchSize;
        }
    }

    private function parseUserResponse(int $id, $response): bool
    {
        // Detect user-not-found message on otherwise 200 page
        if (strpos($response->body(), 'Пользователь не найден.') !== false || $response->status() === 404) {
            return false;
        }

        $dom = HtmlDomParser::str_get_html($response->body());
        if (! $dom) {
            $this->warn("Failed to parse HTML for user id {$id}");

            return false;
        }

        // Set locale for Russian month names
        Carbon::setLocale('ru');

        // Extract stats rows
        $statsRows = $dom->find('section.page-settings__content--table .page-settings__content--row');

        // Last seen ("Был в сети")
        $lastDate = null;
        try {
            if (isset($statsRows[1])) {
                $lastText = trim($statsRows[1]->find('p', 0)->text());
                if (str_contains($lastText, 'Никогда')) {
                    $lastDate = null;
                } elseif (str_contains($lastText, 'Сегодня')) {
                    preg_match('/(\d{2}:\d{2})/', $lastText, $timeMatch);
                    $lastDate = Carbon::today()->setTimeFromTimeString($timeMatch[1]);
                } else {
                    $normalized = str_replace(['в ', ','], '', $lastText);
                    if (! preg_match('/\d{4}/', $normalized)) {
                        if (preg_match('/^(\d{1,2})\s+(\p{L}+)\s+(\d{2}:\d{2})$/u', $normalized, $parts)) {
                            $normalized = "{$parts[1]} {$parts[2]} ".Carbon::now()->year." {$parts[3]}";
                        } else {
                            $normalized .= ' '.Carbon::now()->year;
                        }
                    }
                    $rus = ['января', 'февраля', 'марта', 'апреля', 'мая', 'июня', 'июля', 'августа', 'сентября', 'октября', 'ноября', 'декабря'];
                    $eng = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
                    $normalizedEn = str_ireplace($rus, $eng, mb_strtolower($normalized, 'UTF-8'));
                    $lastDate = Carbon::createFromFormat('j F Y H:i', $normalizedEn)->locale('ru');
                }
                $this->info('Был в сети: '.($lastDate?->diffForHumans() ?? $lastText));
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        // Shop detection
        $is_shop = false;
        try {
            $user_name = $dom->find('.shop-profile .h3', 0)?->plaintext;
            if ($user_name) {
                $is_shop = true;
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        // Items
        $element = $dom->find('.main__item--desc hgroup a', 0);
        if ($element) {
            $this->line("У пользователя {$id} есть объявления");
            $this->parseItem($element, $lastDate, $is_shop);
        } else {
            $this->line("У пользователя {$id} нет объявлений");
        }

        return true;
    }

    /**
     * @throws ConnectionException
     */
    private function parseItem($element, $lastDate = null, $is_shop = false): void
    {
        $href = "https://gunsbroker.ru$element->href";
        $service = new GunsbrokerService;
        $indexModel = $service->parsePost($href, null);
        $this->line($indexModel->title);

        try {
            IndexGunsBrokerUser::withoutRefresh()->updateOrCreate(
                ['user_phone' => $indexModel->user_phone],
                [
                    'type' => $is_shop ? 'shop' : 'user',
                    'user_phone' => $indexModel->user_phone,
                    'user_name' => $indexModel->user_name,
                    'user_id' => $indexModel->user_id,
                    'user_avatar' => $indexModel->user_avatar,
                    'last_activity_at' => $lastDate,
                ]
            );
            $this->info("User {$indexModel->user_phone} indexed");
        } catch (\Elastic\Elasticsearch\Exception\ClientResponseException $e) {
            $this->error("Elasticsearch indexing failed for user {$indexModel->user_id}: ".$e->getMessage());
        }
    }
}
