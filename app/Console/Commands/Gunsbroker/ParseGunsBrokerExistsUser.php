<?php

namespace App\Console\Commands\Gunsbroker;

use App\Models\ImportPostQueue;
use App\Models\Index\IndexGunsBrokerUser;
use App\Services\Parse\GunsbrokerService;
use Illuminate\Console\Command;

class ParseGunsBrokerExistsUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'parse:gunsbroker:user:exists';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $users = ImportPostQueue::distinct()->pluck('user_phone');

        $service = new GunsbrokerService;

        $this->withProgressBar($users, function ($user_phone) use ($service) {
            try {
                $user = IndexGunsBrokerUser::where('user_phone', $user_phone)->firstOrFail();
                $data = $service->parseUserProfile("https://gunsbroker.ru/users/{$user->user_id}");

                if (count($data['posts'])) {
                    foreach ($data['posts'] as $href) {
                        ImportPostQueue::firstOrCreate([
                            'user_name' => $data['name'],
                            'user_avatar' => $data['avatar'],
                            'user_phone' => $user_phone,
                            'source_name' => 'gunsbroker',
                            'source' => $href,
                        ]);
                    }
                }
            } catch (\Throwable $throwable) {
                $this->error($throwable->getMessage());
            }
        });
    }
}
