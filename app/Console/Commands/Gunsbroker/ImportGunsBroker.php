<?php

namespace App\Console\Commands\Gunsbroker;

use App\Events\PostChangeEvent;
use App\Models\Index\IndexGunsBrokerPost;
use App\Models\Post;
use App\Services\Parse\GunsbrokerService;
use Illuminate\Console\Command;

class ImportGunsBroker extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:gunsbroker';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $service = new GunsbrokerService;
        IndexGunsBrokerPost::query()
            ->whereNotNull('user_phone')
            ->chunkById(100, function ($collection) use ($service) {
                foreach ($collection as $product) {
                    try {
                        $this->processPhotos($service, $product);
                    } catch (\Throwable $throwable) {
                        $this->error($throwable->getMessage());
                    }
                }
            });
    }

    /**
     * @throws \Throwable
     */
    private function processPhotos(GunsbrokerService $service, IndexGunsBrokerPost $product): void
    {
        $post = Post::withoutGlobalScopes()->where('source', $product->source)->first();
        if (! $post) {
            return;
        }

        $this->info($post->title);
        $photos = $product->photos;

        foreach ($photos as $key => $photo) {
            $filename = md5($photo['url']);
            $medias = $post->getMedia('images');

            $has = $medias->where('name', $filename)->first();
            if ($has?->path) {
                continue;
            }

            if (str_contains($photo['url'], 'gunsbroker.ru')) {
                try {
                    $this->line('Process photo: '.$photo['url']);
                    $media = $service->removeWatermark($post, $photo['url']);
                    $photos[$key]['url'] = $media?->getUrl(conversion: 'full', fallback: true) ?? $photo['url'];

                    // Сразу обновим в эластике
                    $product->photos = $photos;
                    $product->withoutRefresh()->save();

                    PostChangeEvent::dispatch($post);
                } catch (\Throwable $throwable) {
                    $this->error($throwable->getMessage());
                }
            }
        }
    }
}
