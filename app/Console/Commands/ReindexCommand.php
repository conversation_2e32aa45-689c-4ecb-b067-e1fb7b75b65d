<?php

namespace App\Console\Commands;

use App\Events\PostChangeEvent;
use App\Models\Post;
use Illuminate\Console\Command;

class ReindexCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:reindex';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Реиндексация всех POST';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $posts = Post::get();

        $this->withProgressBar($posts, function (Post $post) {
            PostChangeEvent::dispatch($post);
        });
    }
}
