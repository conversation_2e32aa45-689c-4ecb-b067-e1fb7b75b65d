<?php

namespace App\Console\Commands;

use App\Models\Article;
use App\Models\ArticleCategory;
use App\Models\News;
use App\Services\EditorJsService;
use Illuminate\Console\Command;

class MoveNewsToArticles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:move-news-to-articles';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Переносит новости в статьи с преобразованием HTML контента в формат EditorJS';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $news = News::get();

        $this->info("Найдено {$news->count()} новостей для переноса в статьи");

        $this->withProgressBar($news, function (News $news) {
            $this->makeArticle($news);
        });

        $this->newLine();
        $this->info('Перенос новостей в статьи завершен!');
    }

    private function makeArticle(News $news)
    {
        $article = Article::where('slug', $news->slug)->first();

        // Преобразуем HTML контент в JSON формат EditorJS
        $editorJsContent = EditorJsService::parseHtml($news->content);

        if (!$article) {
            // Создаем новую статью, если её не существует
            Article::create([
                'slug' => $news->slug,
                'user_id' => 4,
                'category_id' => 20,
                'title' => $news->title,
                'cover_image' => $news->image,
                'status' => 'published',
                'content' => $editorJsContent,
                'excerpt' => $news->description,
                'published_at' => $news->published_at,
                'created_at' => $news->created_at,
                'updated_at' => $news->updated_at,
            ]);
        } else {
            // Обновляем существующую статью
            $article->update([
                'user_id' => 4,
                'category_id' => 20,
                'title' => $news->title,
                'cover_image' => $news->image,
                'status' => 'published',
                'content' => $editorJsContent,
                'excerpt' => $news->description,
                'published_at' => $news->published_at,
                'created_at' => $news->created_at,
                'updated_at' => $news->updated_at,
            ]);
        }
    }
}
