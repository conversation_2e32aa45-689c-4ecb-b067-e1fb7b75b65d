<?php

namespace App\Console\Commands;

use App\Models\Post;
use App\Models\PromotionType;
use App\Services\PromotionService;
use Illuminate\Console\Command;

class ActivatePromotionFreeCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'promotion:activate {slug : Slug объявления} {type : Тип продвижения (vip|up|color)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Активировать платное продвижение для объявления по slug без списания средств';

    public function __construct(private PromotionService $promotionService)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $slug = (string) $this->argument('slug');
        $type = (string) $this->argument('type');

        if (! in_array($type, ['vip', 'up', 'color'], true)) {
            $this->error('Неверный тип. Допустимые: vip, up, color');

            return self::FAILURE;
        }

        $post = Post::where('slug', $slug)->first();
        if (! $post) {
            $this->error("Объявление со slug '{$slug}' не найдено");

            return self::FAILURE;
        }

        $promotionType = PromotionType::where('type', $type)->first();
        if (! $promotionType) {
            $this->error("Тип продвижения '{$type}' не найден");

            return self::FAILURE;
        }

        try {
            $promotion = $this->promotionService->createPromotion($post, $promotionType);
            $this->promotionService->activateWithoutPayment($promotion);
        } catch (\Throwable $e) {
            $this->error('Ошибка активации: '.$e->getMessage());

            return self::FAILURE;
        }

        $this->info("Продвижение '{$type}' для объявления '{$slug}' успешно активировано без оплаты.");

        return self::SUCCESS;
    }
}
