<?php

namespace App\Console\Commands;

use App\Services\TelegramHandleService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use NotificationChannels\Telegram\TelegramUpdates;

class TelegramTestCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:test {--mode=updates : Режим работы (updates - получение обновлений, callback - эмуляция callback)}
                            {--post_id= : ID объявления для эмуляции callback}
                            {--action=approve_post : Действие для эмуляции (approve_post или ban_post)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Тестирование обработки Telegram-обновлений и колбэков в локальной среде';

    /**
     * Execute the console command.
     */
    public function handle(TelegramHandleService $telegramService)
    {
        $mode = $this->option('mode');

        if ($mode === 'updates') {
            $this->handleUpdates($telegramService);
        } elseif ($mode === 'callback') {
            $this->handleCallbackEmulation($telegramService);
        } else {
            $this->error("Неизвестный режим: $mode");

            return 1;
        }

        return 0;
    }

    /**
     * Получение и обработка обновлений от Telegram API
     */
    private function handleUpdates(TelegramHandleService $telegramService)
    {
        $this->info('Получение обновлений от Telegram API...');

        try {
            $updates = TelegramUpdates::create()
                ->limit(100)
                ->options(['timeout' => 0])
                ->get();

            if (! $updates['ok']) {
                $this->error('Ошибка при получении обновлений: '.($updates['description'] ?? 'Неизвестная ошибка'));

                return;
            }

            $count = count($updates['result'] ?? []);
            $this->info("Получено $count обновлений.");

            if ($count === 0) {
                $this->warn('Обновлений нет. Возможно, бот не получал сообщений или все обновления уже обработаны.');
                $this->info('Совет: отправьте сообщение боту и запустите команду снова.');

                return;
            }

            $this->table(
                ['ID', 'Тип', 'От', 'Содержимое'],
                $this->formatUpdates($updates['result'])
            );

            if ($this->confirm('Обработать эти обновления?', true)) {
                foreach ($updates['result'] as $update) {
                    $this->info('Обработка обновления #'.$update['update_id']);
                    $telegramService->handleTelegramWebhook($update);
                }
                $this->info('Все обновления обработаны.');
            }
        } catch (\Exception $e) {
            $this->error('Произошла ошибка: '.$e->getMessage());
            Log::error('Ошибка в TelegramTestCommand', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Эмуляция callback-запроса
     */
    private function handleCallbackEmulation(TelegramHandleService $telegramService)
    {
        $postId = $this->option('post_id');
        $action = $this->option('action');

        if (! $postId) {
            $postId = $this->ask('Введите ID объявления для эмуляции callback');
        }

        if (! in_array($action, ['approve_post', 'ban_post'])) {
            $action = $this->choice(
                'Выберите действие для эмуляции',
                ['approve_post', 'ban_post'],
                0
            );
        }

        $chatId = config('services.telegram-bot-api.service_chat_id');
        if (! $chatId) {
            $chatId = $this->ask('Введите ID чата для отправки уведомления');
        }

        $this->info("Эмуляция callback-запроса для объявления #$postId с действием '$action'...");

        // Создаем эмуляцию callback-запроса
        $callbackData = [
            'id' => '12345', // Фиктивный ID
            'from' => [
                'id' => 123456789, // Фиктивный ID пользователя
                'first_name' => 'Test',
                'last_name' => 'User',
                'username' => 'testuser',
            ],
            'message' => [
                'message_id' => 123,
                'chat' => [
                    'id' => $chatId,
                    'type' => 'private',
                ],
                'date' => time(),
                'text' => 'Тестовое сообщение',
            ],
            'chat_instance' => '1234567890',
            'data' => "$action $postId",
        ];

        $update = [
            'update_id' => 123456789,
            'callback_query' => $callbackData,
        ];

        try {
            $telegramService->handleTelegramWebhook($update);
            $this->info('Callback-запрос успешно обработан.');
        } catch (\Exception $e) {
            $this->error('Произошла ошибка при обработке callback-запроса: '.$e->getMessage());
            Log::error('Ошибка в TelegramTestCommand при эмуляции callback', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'update' => $update,
            ]);
        }
    }

    /**
     * Форматирование обновлений для вывода в таблицу
     */
    private function formatUpdates(array $updates): array
    {
        $formatted = [];

        foreach ($updates as $update) {
            $type = $this->getUpdateType($update);
            $from = $this->getUpdateSender($update, $type);
            $content = $this->getUpdateContent($update, $type);

            $formatted[] = [
                'id' => $update['update_id'],
                'type' => $type,
                'from' => $from,
                'content' => $content,
            ];
        }

        return $formatted;
    }

    /**
     * Определение типа обновления
     */
    private function getUpdateType(array $update): string
    {
        if (isset($update['message'])) {
            return 'message';
        } elseif (isset($update['callback_query'])) {
            return 'callback_query';
        } elseif (isset($update['edited_message'])) {
            return 'edited_message';
        } elseif (isset($update['channel_post'])) {
            return 'channel_post';
        } elseif (isset($update['edited_channel_post'])) {
            return 'edited_channel_post';
        } elseif (isset($update['inline_query'])) {
            return 'inline_query';
        } elseif (isset($update['chosen_inline_result'])) {
            return 'chosen_inline_result';
        } elseif (isset($update['shipping_query'])) {
            return 'shipping_query';
        } elseif (isset($update['pre_checkout_query'])) {
            return 'pre_checkout_query';
        } elseif (isset($update['poll'])) {
            return 'poll';
        } elseif (isset($update['poll_answer'])) {
            return 'poll_answer';
        } else {
            return 'unknown';
        }
    }

    /**
     * Получение отправителя обновления
     */
    private function getUpdateSender(array $update, string $type): string
    {
        switch ($type) {
            case 'message':
                $from = $update['message']['from'] ?? [];
                break;
            case 'callback_query':
                $from = $update['callback_query']['from'] ?? [];
                break;
            case 'edited_message':
                $from = $update['edited_message']['from'] ?? [];
                break;
            default:
                $from = [];
        }

        if (empty($from)) {
            return 'Неизвестно';
        }

        $name = $from['first_name'] ?? '';
        if (! empty($from['last_name'])) {
            $name .= ' '.$from['last_name'];
        }
        if (! empty($from['username'])) {
            $name .= ' (@'.$from['username'].')';
        }

        return $name ?: 'Неизвестно';
    }

    /**
     * Получение содержимого обновления
     */
    private function getUpdateContent(array $update, string $type): string
    {
        switch ($type) {
            case 'message':
                if (isset($update['message']['text'])) {
                    return 'Текст: '.$update['message']['text'];
                } elseif (isset($update['message']['photo'])) {
                    return 'Фото';
                } elseif (isset($update['message']['document'])) {
                    return 'Документ: '.($update['message']['document']['file_name'] ?? 'Без имени');
                } else {
                    return 'Другой тип сообщения';
                }
            case 'callback_query':
                return 'Callback данные: '.($update['callback_query']['data'] ?? 'Нет данных');
            default:
                return 'Содержимое недоступно';
        }
    }
}
