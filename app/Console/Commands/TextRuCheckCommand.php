<?php

namespace App\Console\Commands;

use App\Models\TextRuCheck;
use App\Services\TextRuService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class TextRuCheckCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'textru:check';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Проверить уникальность текстов через text.ru (2 этап)';

    /**
     * Execute the console command.
     */
    public function handle(TextRuService $service)
    {
        $checks = TextRuCheck::whereNot('status', 'done')
            ->whereNotNull('text_ru_uid')
            ->get();

        foreach ($checks as $check) {
            $result = $service->getResult($check->text_ru_uid);
            // dd(json_encode($result, JSON_UNESCAPED_UNICODE));

            if (! empty($result['text_unique'])) {
                $check->result = isset($result['result_json']) ? json_decode($result['result_json']) : null;
                $check->spell_check = isset($result['spell_check']) ? json_decode($result['spell_check']) : null;
                $check->seo_check = $result['seo_check'] ?? null;
                $check->text_unique = (float) $result['text_unique'];
                $check->status = 'done';
                $check->error_code = $result['error_code'] ?? null;
                $check->error_desc = $result['error_desc'] ?? null;
                $check->date_check = isset($result['result_json']['date_check']) ? Carbon::parse($result['result_json']['date_check']) : Carbon::now();

                $check->save();

                $this->info("{$check->id}: {$check->percent}%");
            } elseif (! $result['error_code'] && ! $result['error_code']) {
                $check->status = 'error';
                $check->error_code = $result['error_code'] ?? null;
                $check->error_desc = $result['error_desc'] ?? null;
                $check->result = $result;
                $check->save();

                $this->error("Ошибка для проверки {$check->id}: {$result['error_desc']}");
            } else {
                $this->warn("{$check->id}: результат не получен");
            }
        }
    }
}
