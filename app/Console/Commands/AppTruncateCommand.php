<?php

namespace App\Console\Commands;

use App\Models\Index\IndexGeoItems;
use App\Models\Index\IndexGetPhone;
use App\Models\Index\IndexPost;
use App\Models\Index\IndexViews;
use App\Models\Post;
use App\Models\PostAttribute;
use App\Models\User;
use Illuminate\Console\Command;

class AppTruncateCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:truncate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->warn('IndexGeoItems');
        IndexGeoItems::truncate();

        $this->warn('IndexGetPhone');
        IndexGetPhone::truncate();

        $this->warn('IndexPost');
        IndexPost::truncate();

        $this->warn('Post');
        Post::truncate();

        $this->warn('PostAttribute');
        PostAttribute::truncate();

        $this->warn('User');
        User::truncate();

        $this->warn('IndexViews');
        IndexViews::truncate();

    }
}
