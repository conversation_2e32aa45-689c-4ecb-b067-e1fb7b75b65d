<?php

namespace App\Console\Commands;

use App\Models\ConversationParticipant;
use App\Models\User;
use App\Notifications\UnreadMessageTelegram;
use App\Services\TelegramService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Lang;
use Vinkla\Hashids\Facades\Hashids;

class CheckUnreadMessagesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'messages:check-unread {--minutes=30 : Время в минутах, после которого отправлять уведомление}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Проверяет непрочитанные сообщения и отправляет уведомления в Telegram';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $minutes = $this->option('minutes');
        $this->info("Проверка сообщений, непрочитанных более {$minutes} минут...");

        if (env('APP_ENV') === 'local') {
            $minutes = 0;
        }

        $timeThreshold = Carbon::now()->subMinutes($minutes);

        $notificationCount = 0;

        // Получаем участников с непрочитанными сообщениями
        $participants = ConversationParticipant::with(['thread.messages' => function ($query) use ($timeThreshold) {
            $query->where('created_at', '<=', $timeThreshold);
        }, 'thread.messages.user', 'user', 'user.telegram'])
            ->where(function ($query) use ($timeThreshold) {
                $query->whereNull('last_read')
                    ->orWhereHas('thread.messages', function ($q) use ($timeThreshold) {
                        $q->where('created_at', '<=', $timeThreshold)
                            ->whereRaw('created_at > conversation_participants.last_read');
                    });
            })
            ->get();

        // Группируем непрочитанные сообщения по пользователям
        $userUnreadMessages = [];

        foreach ($participants as $participant) {
            // Получаем пользователя-получателя
            $recipient = $participant->user;
            $userId = $recipient->id;

            // Инициализируем массив для пользователя, если его еще нет
            if (! isset($userUnreadMessages[$userId])) {
                $userUnreadMessages[$userId] = [
                    'user' => $recipient,
                    'unread_count' => 0,
                    'threads' => [],
                    'latest_message' => null,
                    'latest_sender' => null,
                ];
            }

            // Проходим по всем непрочитанным сообщениям в этом чате
            foreach ($participant->thread->messages as $message) {
                // Проверяем, что сообщение не прочитано и не отправлено этим же пользователем
                if ($message->user_id != $participant->user_id &&
                    (! $participant->last_read || $message->created_at > $participant->last_read)) {

                    // Увеличиваем счетчик непрочитанных сообщений
                    $userUnreadMessages[$userId]['unread_count']++;

                    // Добавляем чат в список, если его еще нет
                    if (! in_array($participant->thread_id, $userUnreadMessages[$userId]['threads'])) {
                        $userUnreadMessages[$userId]['threads'][] = $participant->thread_id;
                    }

                    // Сохраняем самое последнее сообщение
                    if ($userUnreadMessages[$userId]['latest_message'] === null ||
                        $message->created_at > $userUnreadMessages[$userId]['latest_message']->created_at) {
                        $userUnreadMessages[$userId]['latest_message'] = $message;
                        $userUnreadMessages[$userId]['latest_sender'] = $message->user;
                    }
                }
            }
        }

        // Отправляем агрегированные уведомления пользователям
        foreach ($userUnreadMessages as $userId => $data) {
            $recipient = $data['user'];
            $unreadCount = $data['unread_count'];
            $threadCount = count($data['threads']);
            $latestMessage = $data['latest_message'];
            $latestSender = $data['latest_sender'];

            // Пропускаем пользователей без непрочитанных сообщений
            if ($unreadCount === 0 || $latestMessage === null) {
                continue;
            }

            // Создаем уникальный идентификатор для набора непрочитанных сообщений
            $messageKey = $latestMessage->id.'-'.implode(',', $data['threads']);
            $notificationKey = "unread_messages_notification:{$userId}:{$messageKey}";
            $notificationSent = Cache::has($notificationKey);

            // Проверяем, не отправляли ли мы уже уведомление об этих сообщениях
            if (! $notificationSent) {
                // Если это пользователь поддержки, отправляем уведомление только в сервисный чат
                if ($recipient->email === User::SUPPORT_USER_EMAIL) {
                    $this->info("Отправка уведомления в сервисный чат о сообщении для поддержки от {$latestSender->name}");
                    $this->notifyServiceChat($latestMessage, $latestSender, $unreadCount, $threadCount);
                    Cache::put($notificationKey, true, Carbon::now()->addDays(30));
                    $notificationCount++;
                }
                // Для обычных пользователей отправляем в Telegram, если он привязан
                elseif ($recipient->telegram && $recipient->telegram->chat_id) {
                    $this->info("Отправка уведомления пользователю {$recipient->name} о {$unreadCount} непрочитанных сообщениях в {$threadCount} чатах");

                    // Отправляем агрегированное уведомление
                    $recipient->notify(new UnreadMessageTelegram($latestMessage, $latestSender, $unreadCount, $threadCount));

                    // Сохраняем информацию об отправленном уведомлении
                    Cache::put($notificationKey, true, Carbon::now()->addDays(30));
                    $notificationCount++;
                }
            }
        }

        $this->info("Отправлено {$notificationCount} уведомлений.");
    }

    /**
     * Отправить уведомление в сервисный чат о новом сообщении для пользователя поддержки
     *
     * @param  object  $message  Сообщение
     * @param  object  $sender  Отправитель
     * @param  int  $unreadCount  Количество непрочитанных сообщений
     * @param  int  $threadCount  Количество чатов с непрочитанными сообщениями
     */
    private function notifyServiceChat($message, $sender, int $unreadCount, int $threadCount): void
    {
        try {
            $serviceChatId = (int) config('services.telegram-bot-api.service_chat_id');
            if (! $serviceChatId) {
                $this->error('ID сервисного чата не настроен в конфигурации');

                return;
            }

            // Формируем текст сообщения
            $messagePreview = mb_strlen($message->body) > 100 ? mb_substr($message->body, 0, 100).'...' : $message->body;
            $threadId = Hashids::encode($message->thread_id);
            $threadUrl = "https://gunpost.ru/inbox/{$threadId}";

            // Формируем заголовок и текст в зависимости от количества сообщений
            if ($unreadCount > 1) {
                // Формируем правильные формы слов для чатов и сообщений
                $chatWord = Lang::choice('чате|чатах|чатах', $threadCount, [], 'ru');
                $messageWord = Lang::choice('непрочитанное сообщение|непрочитанных сообщения|непрочитанных сообщений', $unreadCount, [], 'ru');

                $content = "🔔 *Новые сообщения для поддержки*\n".
                    "{$unreadCount} {$messageWord} в {$threadCount} {$chatWord}\n".
                    "Последнее от: {$sender->name}\n".
                    "Телефон: {$sender->phone}\n".
                    "Сообщение: {$messagePreview}";
            } else {
                $content = "🔔 *Новое сообщение для поддержки*\n".
                    "От: {$sender->name}\n".
                    "Телефон: {$sender->phone}\n".
                    "Сообщение: {$messagePreview}";
            }

            // Отправляем уведомление в сервисный чат
            $telegramService = new TelegramService;
            $telegramService->sendMessage($serviceChatId, $content, ['Открыть чат' => $threadUrl]);

            $this->info('Уведомление отправлено в сервисный чат');
        } catch (\Exception $e) {
            $this->error('Ошибка отправки уведомления в сервисный чат: '.$e->getMessage());
        }
    }
}
