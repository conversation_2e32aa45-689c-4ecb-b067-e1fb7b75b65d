<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NewsResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'slug' => $this->slug,
            'content' => $this->content,
            'description' => $this->description,
            'cover' => $this->getFirstMedia('image')?->getUrl(conversion: '350', fallback: true) ?? null,
            'cover_small' => $this->getFirstMedia('image')?->getUrl(conversion: '50', fallback: true) ?? null,
            'image' => $this->getFirstMedia('image')?->getUrl() ?? null,
            'image_alt' => $this->image_alt,
            'published_at' => $this->published_at?->format('Y-m-d H:i:s'),
            'status' => $this->status,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
            'meta_title' => $this->meta_title,
            'meta_description' => $this->meta_description,
            'meta_keywords' => $this->meta_keywords,
            'check' => new TextRuCheckResource($this->whenLoaded('textRuChecks')),
            'media' => MediaResource::collection($this->whenLoaded('media', function () {
                return $this->getMedia('images');
            }, [])),
        ];
    }
}
