<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Vinkla\Hashids\Facades\Hashids;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => Hashids::encode($this->id),
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'balance' => $this->balance.' ₽',
            'role' => $this->role,
            'theme' => $this->theme,
            'messages' => $this->unreadMessagesCount(),
            'city' => new CityResource($this->whenLoaded('city')),
            'notifications' => $this->whenCounted('unreadNotifications'),
            'favorites' => $this->whenCounted('favorites'),
            'has_password' => (bool) $this->password,
            'support_chat_id' => $this->support_chat_id ? Hashids::encode($this->support_chat_id) : null,
            'avatar' => [
                'src' => $this->getFirstMedia('avatar')?->getUrl(conversion: '50', fallback: true) ?? '/images/avatar.jpg',
                'alt' => $this->name,
            ],
            'is_shop' => $this->is_shop,
            'confirmations' => UserConfirmationResource::collection($this->whenLoaded('confirmations')),
            'telegram_notification' => (bool) $this->telegram?->chat_id,
        ];
    }
}
