<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

class SimplePostResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'label' => $this->title,
            'description_short' => Str::limit($this->description, 150),
            'to' => "/{$this->category->slug}/{$this->slug}.html",
            'target' => '_blank',
            'price' => $this->price,
            'suffix' => $this->address,
            'avatar' => [
                'src' => $this->getFirstMedia('images')?->getUrl(conversion: '350', fallback: true),
                'alt' => $this->title,
            ],
        ];
    }
}
