<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ReportResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user' => new UserResource($this->whenLoaded('user')),
            'post' => new PostCreateResource($this->whenLoaded('post')),
            'fingerprint' => $this->fingerprint,
            'message' => $this->message,
            'email' => $this->email,
        ];
    }
}
