<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Vinkla\Hashids\Facades\Hashids;

class ConversationThreadResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => Hashids::encode($this->id),
            // Универсальный вывод связанного ресурса
            'source' => $this->whenLoaded('source', function () {
                switch (get_class($this->source)) {
                    case \App\Models\Post::class:
                        return SimplePostResource::make($this->source);
                    case \App\Models\News::class:
                        return new NewsResource($this->source);
                    default:
                        return $this->source;
                }
            }),
            'source_type' => $this->source_type,
            'subject' => $this->subject,
            'description' => $this->description,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'latest_message' => ConversationMessageResource::make($this->whenLoaded('latest')),
            'messages' => ConversationMessageResource::collection($this->whenLoaded('messages')),
            'unread_messages' => $this->userUnreadMessagesCount(Auth::id()),
        ];
    }
}
