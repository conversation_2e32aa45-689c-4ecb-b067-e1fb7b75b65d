<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'icon' => $this->data['icon'] ?? null,
            'avatar' => $this->data['avatar'] ?? null,
            'to' => $this->data['to'] ?? null,
            'created_at' => $this->created_at,
            'read_at' => $this->read_at,
            'title' => $this->data['title'] ?? null,
            'message' => $this->data['message'] ?? null,
        ];
    }
}
