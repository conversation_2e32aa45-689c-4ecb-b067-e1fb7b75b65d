<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PromotionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'price' => $this->price,
            'duration' => $this->duration,
            'purchased_at' => $this->purchased_at,
            'expires_at' => $this->expires_at,
            'type' => new PromotionTypeResource($this->whenLoaded('promotionType')),
        ];
    }
}
