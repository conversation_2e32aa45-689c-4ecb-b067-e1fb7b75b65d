<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Vinkla\Hashids\Facades\Hashids;

class UserPublicResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => Hashids::encode($this->id),
            'name' => $this->name,
            'avatar' => [
                'src' => $this->getFirstMedia('avatar')?->getUrl(conversion: '50', fallback: true) ?? '/images/avatar.jpg',
                'alt' => $this->name,
            ],
            'is_shop' => $this->is_shop,
        ];
    }
}
