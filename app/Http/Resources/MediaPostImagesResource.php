<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MediaPostImagesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'src' => $this->getUrl(
                conversion: 'full',
                fallback: true
            ),
            'width' => $this->getWidth(
                conversion: 'full',
                fallback: true
            ),
            'height' => $this->getHeight(
                conversion: 'full',
                fallback: true
            ),
            'srcSet' => [
                [
                    'src' => $this->getUrl(
                        conversion: '350',
                        fallback: true
                    ),
                    'width' => $this->getWidth(
                        conversion: '350',
                        fallback: true
                    ),
                    'height' => $this->getHeight(
                        conversion: '350',
                        fallback: true
                    ),
                ],
            ],
        ];
    }
}
