<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EventResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'title' => $this->title,
            'slug' => $this->slug,
            'description' => $this->description,
            'cover' => $this->getFirstMedia('image')?->getUrl(conversion: '350', fallback: true) ?? null,
            'cover_small' => $this->getFirstMedia('image')?->getUrl(conversion: '50', fallback: true) ?? null,
            'image' => $this->getFirstMedia('image')?->getUrl() ?? null,
            'image_alt' => $this->image_alt,
            'place' => $this->place,
            'address' => $this->address,
            'address_geo' => $this->address_geo,
            'start_date' => $this->start_date?->format('Y-m-d'),
            'end_date' => $this->end_date?->format('Y-m-d'),
            'content' => $this->content,
            'status' => $this->status,
            'meta_title' => $this->meta_title,
            'meta_description' => $this->meta_description,
            'meta_keywords' => $this->meta_keywords,
            'city' => new CityResource($this->whenLoaded('city')),
        ];
    }
}
