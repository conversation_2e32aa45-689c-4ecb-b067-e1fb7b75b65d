<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CityResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'label' => $this->name,
            'name' => $this->name,
            'name_pred' => $this->name_pred,
            'value' => $this->slug,
            'region' => sprintf('%02d', $this->region_number),
            'posts' => $this->whenCounted('posts'),
            'lat' => $this->lat,
            'lng' => $this->lng,
        ];
    }
}
