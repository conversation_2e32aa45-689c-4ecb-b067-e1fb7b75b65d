<?php

namespace App\Http\Resources;

use App\Http\Resources\Index\IndexGunsBrokerPostResource;
use App\Models\RefModeration;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PostCreateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'title' => $this->title,
            'slug' => $this->slug,
            'description' => $this->description,
            'year' => $this->year,
            'is_trade' => $this->is_trade,
            'is_rebate' => $this->is_rebate,
            'can_ship' => $this->can_ship,
            'registration_type' => $this->registration_type,
            'registration_address' => $this->registration_address,
            'registration_geo' => $this->registration_geo,
            'published_at' => $this->published_at,
            'address' => $this->address,
            'address_geo' => $this->address_geo,
            'price' => $this->price,
            'views' => $this->views,
            'favorites' => $this->favorites,
            'moderation' => RefModeration::MODERATION[$this->moderation_id] ?? 'not_approved',
            'category' => new CategoryResource($this->whenLoaded('category')),
            'city' => new CityResource($this->whenLoaded('city')),
            'images' => SimpleMediaResource::collection($this->getMedia('images')),
            'promotions' => PromotionResource::collection($this->whenLoaded('promotions')),
            'sourceGunsBroker' => new IndexGunsBrokerPostResource($this->whenLoaded('sourceGunsBroker')),
        ];
    }
}
