<?php

namespace App\Http\Resources\Index;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;

class IndexPostResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $arr = Arr::except(parent::toArray($request), [
            '_id',
            'user_id',
            'user_phone',
            'source',
            'updated_at',
            'created_at',
        ]);

        $arr['thumbnails'] = array_splice($arr['thumbnails'], 0, 5);

        return array_filter($arr);
    }
}
