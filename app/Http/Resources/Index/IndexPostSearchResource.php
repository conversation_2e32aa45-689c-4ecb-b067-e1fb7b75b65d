<?php

namespace App\Http\Resources\Index;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class IndexPostSearchResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'label' => $this->title,
            'description_short' => $this->description_short,
            'to' => "/$this->category/$this->slug.html",
            'target' => '_blank',
            'price' => $this->price,
            'suffix' => $this->address,
            'avatar' => [
                'src' => $this->thumbnails[0] ?? null,
            ],
        ];
    }
}
