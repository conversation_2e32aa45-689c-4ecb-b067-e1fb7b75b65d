<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class StaticKeyAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $staticKey = config('services.static_api_key');

        if (empty($staticKey)) {
            return response()->json(['error' => 'Static API key not configured'], 500);
        }

        $providedKey = $request->header('X-API-Key') ?? $request->get('api_key');

        if ($providedKey !== $staticKey) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        return $next($request);
    }
}
