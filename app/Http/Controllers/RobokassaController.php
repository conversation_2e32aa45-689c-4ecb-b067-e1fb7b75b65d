<?php

namespace App\Http\Controllers;

use App\Models\Promotion;
use App\Services\RobokassaService;
use Illuminate\Http\Request;

class RobokassaController extends Controller
{
    protected $robokassaService;

    public function __construct(RobokassaService $robokassaService)
    {
        $this->robokassaService = $robokassaService;
    }

    public function makeServerPayment(Promotion $promotion)
    {
        $result = $this->robokassaService->PromotionPayment($promotion);

        return response()->json($result);
    }

    public function webhook(Request $request)
    {
        $result = $this->robokassaService->handleWebhook($request);

        return $result;
    }
}
