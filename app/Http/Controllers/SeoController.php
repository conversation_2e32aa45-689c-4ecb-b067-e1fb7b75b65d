<?php

namespace App\Http\Controllers;

use App\Http\Requests\PathSeoDataRequest;
use App\Http\Resources\Admin\SeoResource;
use App\Http\Resources\PathSeoDataResource;
use App\Models\PathSeoData;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class SeoController extends Controller
{
    public function index(Request $request)
    {
        if ($request->q) {
            $seo = PathSeoData::where('path', 'like', "%{$request->q}%")->paginate();
        } else {
            $seo = PathSeoData::paginate();
        }

        return SeoResource::collection($seo);
    }

    public function save(PathSeoData $seo, Request $request)
    {
        $data = $request->only(['path',
            'title',
            'meta_description',
            'meta_keywords',
            'h1',
            'content', ]);

        $seo->fill($data);
        $seo->save();

        Cache::delete("seo_data_{$seo->path}");
        Cache::remember("seo_data_{$seo->path}", now()->addHours(PathSeoData::CACHE_TIME), function () use ($seo) {
            return new PathSeoDataResource($seo);
        });

        return response()->json([
            'ok' => true,
        ], 201);
    }

    public function create(PathSeoDataRequest $request)
    {
        $data = $request->only(['path',
            'title',
            'meta_description',
            'meta_keywords',
            'h1',
            'content', ]);

        $seo = PathSeoData::firstOrCreate($data);

        Cache::remember("seo_data_{$seo->path}", now()->addHours(PathSeoData::CACHE_TIME), function () use ($seo) {
            return new PathSeoDataResource($seo);
        });

        return response()->json([
            'ok' => true,
        ], 201);
    }
}
