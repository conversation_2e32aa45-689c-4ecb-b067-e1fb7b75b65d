<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Support\Facades\Auth;

class TelegramController extends Controller
{
    public function startMessage()
    {
        /** @var User $user */
        $user = Auth::user();

        $service = new \App\Services\TelegramHandleService;
        $link = $service->generateStartLink($user);

        return response()->json([
            'ok' => true,
            'link' => $link,
        ]);
    }
}
