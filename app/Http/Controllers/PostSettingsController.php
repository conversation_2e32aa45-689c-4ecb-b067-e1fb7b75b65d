<?php

namespace App\Http\Controllers;

use App\Http\Resources\PostCreateResource;
use App\Models\Index\IndexPost;
use App\Models\Post;
use App\Models\RefModeration;
use App\Services\PostService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class PostSettingsController extends Controller
{
    private PostService $postService;

    public function __construct(PostService $postService)
    {
        $this->postService = $postService;
    }

    public function posts(Request $request): AnonymousResourceCollection
    {
        Validator::make($request->all(), [
            'status' => 'required|in:active,archived,pending,banned',
        ]);

        $posts = Post::withoutGlobalScopes()
            ->where('user_id', Auth::id())
            ->with(['category', 'city', 'promotions', 'promotions.promotionType', 'sourceGunsBroker'])
            ->orderBy('published_at');

        $status = $request->input('status', 'active');

        if ($status === 'active') {
            $posts = $posts->whereNull('archived_at')
                ->where(function ($query) {
                    $query->where('moderation_id', RefModeration::IS_APPROVED)
                        ->orWhere('moderation_id', RefModeration::IS_NOT_APPROVED);
                });
        }

        if ($status === 'archived') {
            $posts = $posts->whereNotNull('archived_at');
        }

        $posts = $posts->paginate();

        return PostCreateResource::collection($posts);
    }

    public function delete(string $slug, Request $request): \Illuminate\Http\JsonResponse
    {
        $post = $this->postService->getPost($slug, $request);

        $post->delete();
        IndexPost::where('slug', $post->slug)->delete();

        return response()->json([
            'ok' => true,
        ], 201);
    }
}
