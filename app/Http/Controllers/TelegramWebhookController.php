<?php

namespace App\Http\Controllers;

use App\Services\TelegramHandleService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class TelegramWebhookController extends Controller
{
    public function handle(Request $request, TelegramHandleService $service): JsonResponse
    {
        $service->handleTelegramWebhook($request->all());

        return response()->json(['ok' => true]);
    }
}
