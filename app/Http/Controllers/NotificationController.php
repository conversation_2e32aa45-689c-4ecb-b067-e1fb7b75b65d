<?php

namespace App\Http\Controllers;

use App\Http\Resources\NotificationResource;
use Illuminate\Http\Request;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    public function getNotifications(Request $request)
    {
        $user = Auth::user();
        $notifications = $user->notifications()->paginate();

        return NotificationResource::collection($notifications);
    }

    public function readNotification(DatabaseNotification $notification)
    {
        $notification->markAsRead();

        return response()->json(['ok' => true, 201]);
    }
}
