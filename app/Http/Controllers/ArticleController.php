<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Resources\ArticleResource;
use App\Models\Article;
use App\Models\ArticleView;
use App\Http\Requests\StoreArticleRequest;
use App\Http\Requests\UpdateArticleRequest;
use BeyondCode\Comments\Comment;
use EditorJS\EditorJS;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class ArticleController extends Controller
{
    private const REACTION_TYPES = ['like', 'love', 'fire', 'wow', 'sad', 'angry'];

    public function uploadImage(Request $request)
    {
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $path = $request->file('image')->store('articles', 's3');

        return response()->json([
            'success' => true,
            'file' => [
                'url' => Storage::url($path),
            ],
        ]);
    }
    public function index(Request $request)
    {
        $query = Article::with(['user', 'category', 'tags'])
            ->published()
            ->orderBy('published_at', 'desc');

        if ($request->has('category')) {
            $query->whereHas('category', function ($q) use ($request) {
                $q->where('slug', $request->get('category'));
            });
        }

        if ($request->has('tag')) {
            $query->withAnyTags($request->get('tag'));
        }

        if ($request->has('featured')) {
            $query->featured();
        }

        $query = $query->paginate(15);

        return ArticleResource::collection($query);
    }

    public function show(Article $article)
    {
        $article->load(['user', 'category', 'tags']);

        // Получаем реакции через новое API Laravel Love
        $reactions = [];
        $reactantFacade = $article->viaLoveReactant();

        // Получаем счетчики реакций
        $reactionCounters = $reactantFacade->getReactionCounters();
        $counters = [];
        foreach ($reactionCounters as $counter) {
            $counters[$counter->reaction_type->name] = $counter->count;
        }

        // Получаем реакцию текущего пользователя
        $userReaction = null;
        if (Auth::check()) {
            $user = Auth::user();
            $reacterFacade = $user->viaLoveReacter();

            // Проверяем все типы реакций
            foreach (self::REACTION_TYPES as $type) {
                if ($reacterFacade->hasReactedTo($article, $type)) {
                    $userReaction = $type;
                    break;
                }
            }
        }

        $reactions = [
            'counters' => $counters,
            'user_reaction' => $userReaction
        ];

        $article->incrementViewCount();

        return response()->json([
            'article' => new ArticleResource($article),
            'comments' => $article->comments()->with('user')->paginate(20),
            'reactions' => $reactions
        ]);
    }

    public function store(StoreArticleRequest $request)
    {
        $configJson = json_encode(config('editorjs.config'));

        // Очищаем пустые поля caption в блоках
        $content = $request->get('content');
        if (isset($content['blocks'])) {
            foreach ($content['blocks'] as &$block) {
                if (isset($block['data']['caption']) && empty($block['data']['caption'])) {
                    unset($block['data']['caption']);
                }
            }
        }

        try {
            new EditorJS(json_encode($content), $configJson);
        } catch (\Throwable $exception) {
            return response()->json([
                'ok' => false,
                'message' => $exception->getMessage(),
            ], 422);
        }

        $article = Article::create([
            'user_id' => Auth::id(),
            'category_id' => $request->get('category_id'),
            'title' => $request->get('title'),
            'excerpt' => $request->get('excerpt'),
            'content' => $content,
            'cover_image' => $request->get('cover_image'),
            'status' => 'moderation',
            'allow_comments' => $request->get('allow_comments', true),
        ]);

        // Добавляем теги
        if ($request->has('tags')) {
            $article->attachTags($request->get('tags'));
        }

        // Вычисляем время чтения
        $article->update(['read_time' => $article->calculateReadTime()]);

        return response()->json($article, 201);
    }

    public function update(Request $request, Article $article)
    {
//        $this->authorize('update', $article);

        $configJson = json_encode(config('editorjs.config'));

        // Очищаем пустые поля caption в блоках
        $content = $request->get('content');
        if (isset($content['blocks'])) {
            foreach ($content['blocks'] as &$block) {
                if (isset($block['data']['caption']) && empty($block['data']['caption'])) {
                    unset($block['data']['caption']);
                }
            }
        }

        try {
            new EditorJS(json_encode($content), $configJson);
        } catch (\Throwable $exception) {
            return response()->json([
                'ok' => false,
                'message' => $exception->getMessage(),
            ], 422);
        }

        $article->update([
            'content' => $content,
        ]);

        if ($request->has('tags')) {
            $article->syncTags($request->get('tags'));
        }

        $article->update(['read_time' => $article->calculateReadTime()]);

        return response()->json([
            'ok' => true
        ]);
    }

    public function destroy(Article $article)
    {
        $this->authorize('delete', $article);

        $article->delete();

        return response()->noContent();
    }

    public function recordView(Article $article, Request $request)
    {
        $ipAddress = $request->ip();
        $userId = Auth::id();

        // Проверяем уникальность просмотра за последние 24 часа
        $recentView = ArticleView::where('article_id', $article->id)
            ->where('ip_address', $ipAddress)
            ->where('viewed_at', '>', now()->subDay())
            ->exists();

        if (!$recentView) {
            ArticleView::create([
                'article_id' => $article->id,
                'user_id' => $userId,
                'ip_address' => $ipAddress,
                'user_agent' => $request->userAgent(),
                'viewed_at' => now(),
            ]);

            $article->incrementViewCount();
        }

        return response()->json(['success' => true]);
    }

    public function react(Article $article, Request $request)
    {
        $request->validate([
            'type' => 'required|in:' . implode(',', self::REACTION_TYPES)
        ]);

        $user = Auth::user();
        $reactionTypeName = $request->get('type');
        $reacterFacade = $user->viaLoveReacter();

        // Проверяем, есть ли уже такая же реакция от этого пользователя
        if ($reacterFacade->hasReactedTo($article, $reactionTypeName)) {
            // Если та же реакция - удаляем
            $reacterFacade->unreactTo($article, $reactionTypeName);
            return response()->json([
                'action' => 'removed',
                'success' => true
            ]);
        }

        // Проверяем, есть ли другие реакции от этого пользователя
        foreach (self::REACTION_TYPES as $type) {
            if ($type !== $reactionTypeName && $reacterFacade->hasReactedTo($article, $type)) {
                // Удаляем старую реакцию
                $reacterFacade->unreactTo($article, $type);
                break;
            }
        }

        // Добавляем новую реакцию
        $reacterFacade->reactTo($article, $reactionTypeName);

        return response()->json([
            'action' => 'added',
            'success' => true
        ]);
    }

    public function unreact(Article $article)
    {
        $user = Auth::user();
        $reacterFacade = $user->viaLoveReacter();

        // Удаляем все реакции пользователя к этой статье
        foreach (self::REACTION_TYPES as $type) {
            if ($reacterFacade->hasReactedTo($article, $type)) {
                $reacterFacade->unreactTo($article, $type);
            }
        }

        return response()->json(['success' => true]);
    }

    public function storeComment(Article $article, Request $request)
    {
        $validated = $request->validate([
            'body' => 'required|string|min:2|max:1000',
            'parent_id' => 'nullable|exists:comments,id'
        ]);

        // Проверяем, разрешены ли комментарии
        if (!$article->allow_comments) {
            return response()->json([
                'error' => 'Comments are disabled for this article'
            ], 403);
        }

        // Создаем комментарий через пакет beyondcode/laravel-comments
        $comment = new Comment();
        $comment->user_id = Auth::id();
        $comment->commentable_type = get_class($article);
        $comment->commentable_id = $article->id;
        $comment->comment = $validated['body'];
        $comment->parent_id = $validated['parent_id'] ?? null;
        $comment->save();

        return response()->json($comment->load('user'), 201);
    }

    public function updateComment(Request $request, $commentId)
    {
        $validated = $request->validate([
            'body' => 'required|string|min:2|max:1000'
        ]);

        $comment = Comment::findOrFail($commentId);

        // Проверяем права
        if ($comment->user_id !== Auth::id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $comment->update([
            'comment' => $validated['body']
        ]);

        return response()->json($comment->load('user'));
    }

    public function deleteComment($commentId)
    {
        $comment = Comment::findOrFail($commentId);

        // Проверяем права (автор или модератор)
        if ($comment->user_id !== Auth::id() && !Auth::user()->isModerator()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $comment->delete();

        return response()->noContent();
    }

    public function getComments(Article $article, Request $request)
    {
        $comments = $article->comments()
            ->whereNull('parent_id') // Только корневые комментарии
            ->with(['user', 'children.user']) // Загружаем вложенные
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 20));

        return response()->json($comments);
    }
}
