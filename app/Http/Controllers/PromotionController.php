<?php

namespace App\Http\Controllers;

use App\Http\Resources\PromotionResource;
use App\Http\Resources\PromotionTypeResource;
use App\Models\Index\IndexPost;
use App\Models\Post;
use App\Models\Promotion;
use App\Models\PromotionType;
use App\Services\Promotion\Exceptions\PromotionAlreadyActiveException;
use App\Services\PromotionService;
use App\Services\RobokassaService;
use Bavix\Wallet\Exceptions\BalanceIsEmpty;
use Bavix\Wallet\Exceptions\InsufficientFunds;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PromotionController extends Controller
{
    public function __construct(
        private PromotionService $promotionService,
        private RobokassaService $robokassaService
    ) {}

    public function getPromotions(IndexPost $post, Request $request)
    {
        $promotions = Promotion::where('post_id', $post->id)
            ->whereNotNull('purchased_at')
            ->whereNotNull('processed_at')
            ->where('created_at', '<', Carbon::now())
            ->with('promotionType')
            ->orderByDesc('updated_at')
            ->paginate();

        $promotionTypes = PromotionType::get();

        return PromotionResource::collection($promotions)->additional([
            'types' => PromotionTypeResource::collection($promotionTypes),
        ]);
    }

    /**
     * @throws PromotionAlreadyActiveException
     */
    public function setPromotion(Post $post, Request $request): JsonResponse
    {
        $request->validate([
            'promotion' => 'required|string|in:vip,up,color',
        ]);

        $promotionType = PromotionType::where('type', $request->promotion)->firstOrFail();
        $promotion = $this->promotionService->createPromotion($post, $promotionType);

        try {
            $this->promotionService->payForPromotion($promotion);

            return response()->json([
                'message' => 'Продвижение успешно активировано',
                'promotion' => new PromotionResource($promotion),
            ]);
        } catch (PromotionAlreadyActiveException $e) {
            return response()->json(['message' => 'Продвижение уже активно'], 400);
        } catch (BalanceIsEmpty|InsufficientFunds $e) {
            return response()->json([
                'success' => false,
                'message' => 'Недостаточно средств. Пополните баланс',
                'promotion' => new PromotionResource($promotion),
            ], 402);
        } catch (\Exception $e) {
            return response()->json(['message' => $e->getMessage()], 500);
        }
    }
}
