<?php

namespace App\Http\Controllers;

use App\Http\Requests\NewsRequest;
use App\Http\Resources\NewsResource;
use App\Models\News;
use App\Services\TextRuService;
use Elegantly\Media\Models\Media;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Log;

class NewsController extends Controller
{
    public function index(): AnonymousResourceCollection
    {
        $limit = request()->get('limit', 16);
        $news = News::orderByDesc('published_at')
            ->where('status', 'published')
            ->with('media')
            ->paginate($limit);

        return NewsResource::collection($news);
    }

    public function show(News $news): NewsResource
    {
        $news->load('media');

        // other news
        $otherNews = News::orderByDesc('published_at')
            ->where('status', 'published')
            ->where('id', '!=', $news->id)
            ->limit(10)
            ->get();

        return (new NewsResource($news))->additional([
            'other' => NewsResource::collection($otherNews),
        ]);
    }

    public function moderationShow(News $news): NewsResource
    {
        $news->load('media', 'textRuChecks');

        return new NewsResource($news);
    }

    public function moderationIndex(): AnonymousResourceCollection
    {
        $limit = request()->get('limit', 16);
        $news = News::orderByDesc('published_at')
            ->with(['media', 'textRuChecks'])
            ->paginate($limit);

        return NewsResource::collection($news);
    }

    public function store(NewsRequest $request): JsonResponse
    {
        $data = $request->validated();
        if (empty($data['published_at'])) {
            $data['published_at'] = now();
        }

        $news = News::create($data);

        if ($request->hasFile('image_new')) {
            try {
                $news->addMedia(
                    file: $request->file('image_new'),
                    collectionName: 'image',
                    name: "{$news->id}-image"
                );
            } catch (\Throwable $exception) {
                Log::error($exception);
            }
        }

        return response()->json([
            'ok' => true,
            'slug' => $news->slug,
        ], 201);
    }

    public function update(NewsRequest $request, News $news): JsonResponse
    {
        $data = $request->validated();
        if (empty($data['published_at'])) {
            $data['published_at'] = now();
        }
        $news->update($data);

        if ($request->hasFile('image_new')) {
            try {
                $news->addMedia(
                    file: $request->file('image_new'),
                    collectionName: 'image',
                    name: "{$news->id}-image"
                );
            } catch (\Throwable $exception) {
                Log::error($exception);
            }
        }

        return response()->json([
            'ok' => true,
            'slug' => $news->slug,
        ], 201);
    }

    public function destroy(News $news): JsonResponse
    {
        $news->delete();

        return response()->json([
            'ok' => true,
        ], 201);
    }

    public function uploadImage(News $news, Request $request)
    {
        foreach ($request->photos ?? [] as $image) {
            try {
                $news->addMedia(
                    file: $image,
                    collectionName: 'images',
                );
            } catch (\Throwable $exception) {
                // Will throw an error if the mime type is not included in the collection's `acceptedMimeTypes` parameter.
            }
        }

        return response()->json([
            'ok' => true,
        ], 201);
    }

    public function removeImage(News $news, Request $request)
    {
        $media = Media::where('uuid', $request->id)
            ->where('model_type', 'App\Models\News')
            ->where('model_id', $news->id)
            ->first();

        $media->delete();

        return response()->json([
            'ok' => true,
        ], 201);
    }

    /**
     * Отправить новость на проверку уникальности (admin)
     */
    public function sendToTextRu(News $news, TextRuService $textRuService): JsonResponse
    {
        // Проверяем, не отправлялась ли уже новость на проверку
        if ($news->textRuChecks()->whereNot('status', 'done')->exists()) {
            return response()->json([
                'ok' => false,
                'message' => 'Проверка уже запущена',
            ], 409);
        }

        // Отправляем текст на проверку
        $uid = $textRuService->sendText($news->content);
        if (! $uid) {
            return response()->json([
                'ok' => false,
                'message' => 'Ошибка отправки запроса к text.ru',
            ], 500);
        }

        // Создаём запись проверки
        $check = $news->textRuChecks()->create([
            'text_ru_uid' => $uid,
            'status' => 'pending',
        ]);

        return response()->json([
            'ok' => true,
            'check_id' => $check->id,
        ]);
    }
}
