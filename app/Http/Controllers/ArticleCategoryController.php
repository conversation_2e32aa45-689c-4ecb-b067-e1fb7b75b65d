<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\ArticleCategory;
use Illuminate\Http\Request;

class ArticleCategoryController extends Controller
{
    public function index()
    {
        $categories = ArticleCategory::active()
            ->withCount('articles')
            ->orderBy('sort_order')
            ->get();

        return response()->json($categories);
    }

    public function show(ArticleCategory $category)
    {
        $category->loadCount('articles');

        $articles = $category->articles()
            ->with(['user', 'tags'])
            ->published()
            ->orderBy('published_at', 'desc')
            ->paginate(15);

        return response()->json([
            'category' => $category,
            'articles' => $articles
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:255',
            'icon' => 'nullable|string|max:50',
            'sort_order' => 'integer',
            'is_active' => 'boolean'
        ]);

        $category = ArticleCategory::create($request->all());

        return response()->json($category, 201);
    }

    public function update(Request $request, ArticleCategory $category)
    {
        $request->validate([
            'name' => 'string|max:100',
            'description' => 'nullable|string|max:255',
            'icon' => 'nullable|string|max:50',
            'sort_order' => 'integer',
            'is_active' => 'boolean'
        ]);

        $category->update($request->all());

        return response()->json($category);
    }

    public function destroy(ArticleCategory $category)
    {
        // Проверяем, есть ли статьи в категории
        if ($category->articles()->exists()) {
            return response()->json([
                'message' => 'Cannot delete category with articles'
            ], 422);
        }

        $category->delete();

        return response()->noContent();
    }
}
