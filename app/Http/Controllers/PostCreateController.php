<?php

namespace App\Http\Controllers;

use App\Events\PostChangeEvent;
use App\Http\Requests\UpdateCreatePostRequest;
use App\Http\Requests\UpdatePostImagesRequest;
use App\Http\Resources\CityResource;
use App\Http\Resources\PostCreateResource;
use App\Models\Category;
use App\Models\Index\IndexPost;
use App\Models\PathSeoData;
use App\Models\Post;
use App\Models\PostArchivedReason;
use App\Models\PostAttribute;
use App\Models\RefCity;
use App\Models\RefModeration;
use App\Services\PostService;
use Carbon\Carbon;
use Elegantly\Media\Models\Media;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class PostCreateController extends Controller
{
    private PostService $postService;

    public function __construct(PostService $postService)
    {
        $this->postService = $postService;
    }

    public function approveToggle(string $postSlug, Request $request): JsonResponse
    {
        $post = $this->postService->getPost($postSlug, $request);

        if (! $post->published_at) {
            // Правила только для публикации (всё, что должно быть заполнено)
            $rules = [
                'title' => 'required|string|max:255',
                'description' => 'required|string',
                'price' => 'required|integer|min:0',
                'year' => 'nullable|numeric|max:2024',
                'address' => 'nullable|string',
                'ref_city_id' => 'required|exists:ref_cities,id',
            ];

            $attributes = [
                'title' => 'заголовок',
                'description' => 'описание',
                'price' => 'цена',
                'year' => 'год',
                'ref_city_id' => 'город',
            ];

            if ($post->media()->count() < 1) {
                return response()->json([
                    'ok' => false,
                    'message' => 'Добавьте как минимум одну фотографию перед публикацией.',
                ], 422);
            }

            if ($post->registration_type && ! $post->registration_address) {
                return response()->json([
                    'ok' => false,
                    'message' => 'Укажите адрес переоформления перед публикацией.',
                ], 422);
            }

            // Превращаем модель в массив
            $data = $post->toArray();

            $validator = Validator::make($data, $rules, [], $attributes);

            if ($validator->fails()) {
                return response()->json([
                    'ok' => false,
                    'message' => 'Заполните обязательные поля перед публикацией.',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $post->post_archived_reason_id = null;
            $post->archived_at = null;
            $post->archived_comment = null;
            $post->published_at = $post->created_at;
        } else {
            $reason = PostArchivedReason::find($request->reason)?->first();
            $post->post_archived_reason_id = $reason->id;
            $post->archived_at = Carbon::now();
            $post->archived_comment = $request->reason_comment;
            $post->published_at = null;
        }

        $post->save();
        PostChangeEvent::dispatch($post);

        return response()->json(['ok' => true], 201);
    }

    public function removeImage(string $postSlug, Request $request)
    {
        $post = $this->postService->getPost($postSlug, $request);

        $media = Media::where('uuid', $request->id)
            ->where('model_type', 'App\Models\Post')
            ->where('model_id', $post->id)
            ->first();

        $media->delete();

        return response()->json([
            'ok' => true,
        ], 201);
    }

    public function updateCreate(string $postSlug, UpdateCreatePostRequest $request)
    {
        $user_id = auth('sanctum')->id();
        $post = $this->postService->getPost($postSlug, $request);

        if (! $post?->slug) {
            return response()->json([
                'ok' => false,
                'message' => 'Объявление не найдено',
            ], 422);
        }

        $attrs = $this->postService->getFilters($post->category->slug);

        if ($request->city) {
            $city = RefCity::where('slug', $request->city)->first();
            $post->ref_city_id = $city?->id;
        }

        if ($request->title) {
            $post->title = $request->title;
            $post->slug = $post->id.'-'.Str::slug($request->title);
        }

        $data = $request->only([
            'year',
            'description',
            'price',
            'is_rebate',
            'is_trade',
            'can_ship',
            'registration_type',
        ]);

        $post->fill($data);

        if (! $post->user_id && $user_id) {
            $post->user_id = $user_id;
        }

        if ($request->data_registration_address) {
            $post->registration_address = $request->data_registration_address['value'];
            $post->registration_geo = [$request->data_registration_address['geo_lat'], $request->data_registration_address['geo_lon']];
        }

        if ($request->data_address) {
            $post->address = $request->data_address['value'];
            $post->address_geo = [$request->data_address['geo_lat'], $request->data_address['geo_lon']];
        }

        if ($request->is_final) {
            $post->published_at = now();
        }

        $attributes = Category::getFiltersFor($post->category->slug);

        if ($request->has('attributes')) {
            foreach ($request->input('attributes', []) as $attribute_name => $attribute_value) {
                if (isset($attributes[$attribute_name]['values'])) {
                    $model = resolve($attributes[$attribute_name]['values']);
                    $model_value = $model->where('slug', $attribute_value)->first();

                    if ($model_value) {
                        $attribute = PostAttribute::firstOrCreate([
                            'post_id' => $post->id,
                            'attributeable_type' => $attributes[$attribute_name]['values'],
                            'attributeable_id' => $model_value->id,
                        ]);

                        PostAttribute::where('post_id', $post->id)
                            ->where('attributeable_type', $attributes[$attribute_name]['values'])
                            ->whereNot('id', $attribute->id)
                            ->delete();
                    }
                }
            }
        }

        if ($post->isDirty()) {
            $post->moderation_id = RefModeration::IS_NOT_APPROVED;
        }

        $post->save();

        $post->load(['category', 'city', 'attributes', 'attributes.attributeable', 'media']);
        PostChangeEvent::dispatch($post);

        return response()->json([
            'ok' => true,
            'slug' => $post->slug,
            'attrs' => $attrs,
            'post' => new PostCreateResource($post),
        ]);
    }

    public function updateOrCreateImage(UpdatePostImagesRequest $request, string $postSlug): JsonResponse
    {
        $post = $this->postService->getPost($postSlug, $request);

        if (! $post?->slug or ! $request->hasFile('photo')) {
            return response()->json([
                'ok' => false,
                'message' => 'Объявление не найдено',
            ], 422);
        }
        $media = $post->addMedia(
            file: $request->file('photo'),
            collectionName: 'images',
        );

        if ($media) {
            $post->moderation_id = RefModeration::IS_APPROVED;
            $post->save();
        }

        return response()->json([
            'ok' => true,
            'id' => $media->uuid,
        ]);
    }

    /**
     * Получить текст ошибки загрузки файла по коду
     *
     * @param  int  $errorCode  Код ошибки загрузки
     * @return string Текст ошибки
     */
    private function getUploadErrorMessage(int $errorCode): string
    {
        return match ($errorCode) {
            UPLOAD_ERR_INI_SIZE => 'Размер файла превышает максимально допустимый размер, указанный в php.ini',
            UPLOAD_ERR_FORM_SIZE => 'Размер файла превышает максимально допустимый размер, указанный в форме',
            UPLOAD_ERR_PARTIAL => 'Файл был загружен только частично',
            UPLOAD_ERR_NO_FILE => 'Файл не был загружен',
            UPLOAD_ERR_NO_TMP_DIR => 'Отсутствует временная папка для загрузки',
            UPLOAD_ERR_CANT_WRITE => 'Не удалось записать файл на диск',
            UPLOAD_ERR_EXTENSION => 'Загрузка файла была остановлена расширением PHP',
            default => 'Неизвестная ошибка при загрузке файла',
        };
    }

    public function continueCreate(string $postSlug, Request $request): JsonResponse
    {
        $post = $this->postService->getPost($postSlug, $request);

        // if ($post->moderation_id !== RefModeration::IS_NOT_APPROVED) {
        //     return response()->json([
        //         'ok' => false,
        //         'message' => 'Пост уже опубликован.',
        //     ], 422);
        // }

        $attrs = $this->postService->getFilters($post->category);

        $post->load(['category', 'city', 'attributes', 'attributes.attributeable', 'media']);

        $filter_attributes = [];

        foreach ($post->attributes as $attribute) {

            $categorySlug = $post->category->slug;
            $filters = Category::getFiltersFor($categorySlug);

            $attributeType = $attribute->attributeable_type;

            foreach ($filters as $filterKey => $filter) {
                if (! empty($filter['values']) && $filter['values'] === $attributeType) {
                    $filter_attributes[$filterKey] = $attribute->attributeable->slug;
                    break;
                }
            }
        }

        return response()->json([
            'ok' => true,
            'slug' => $post->slug,
            'attributes' => $filter_attributes,
            'post' => new PostCreateResource($post),
            'attrs' => $attrs,
        ]);
    }

    public function create(Request $request): JsonResponse
    {
        $user_id = auth('sanctum')->id();

        if (! $user_id) {
            $user_id = Auth::id();
        }

        if (! $user_id) {
            return response()->json([
                'ok' => false,
                'message' => 'Необходимо авторизоваться',
            ], 401);
        }

        $category = Category::where('slug', $request->category)->first();

        $post = Post::withoutGlobalScopes()->firstOrCreate([
            'title' => $request->title,
            'user_id' => $user_id,
            'category_id' => $category->id,
            'fingerprint' => $request->uuid,
        ]);

        $post->slug = $post->id.'-'.Str::slug($post->title);
        $post->save();

        $attrs = $this->postService->getFilters($category);

        return response()->json([
            'ok' => true,
            'slug' => $post->slug,
            'attrs' => $attrs,
        ]);
    }

    public function update(Post $post): JsonResponse
    {
        return response()->json([
            'id' => $post->id,
            'slug' => $post->slug,
        ]);
    }

    public function getCities(Request $request)
    {
        // Кэш для списка городов
        $cities = Cache::remember('cities', now()->addHours(PathSeoData::CACHE_TIME), function () {
            $cities = RefCity::orderBy('order')->withCount('posts')->get();

            return CityResource::collection($cities);
        });

        $lat = $request->header('Cf-Iplatitude') ?? null;
        $lon = $request->header('Cf-Iplongitude') ?? null;
        $ip = $request->header('Cf-Connecting-Ip') ?? $request->ip();

        // Кэш для города по IP
        if (config('app.env') === 'local') {
            $lat = '59.89830';
            $lon = '30.26180';
            $ip = '************';
        }

        $cacheKey = 'city_cf_ip_'.$ip;
        $cityData = Cache::get($cacheKey);

        if (! $cityData && $lat && $lon) {
            try {
                $post = IndexPost::where('published_at', '!=', null)
                    ->whereGeoDistance('address_geo', '5000km', [(float) $lat, (float) $lon])
                    ->first();

                if ($post?->city_slug) {
                    $cityData = [
                        'city_slug' => $post?->city_slug,
                        'city_name' => $post?->city_name,
                    ];
                    Cache::forever($cacheKey, $cityData);
                }
            } catch (\Throwable $throwable) {
                Log::error($throwable->getMessage());
            }
        }

        if (! $cityData) {
            $cityData = [
                'city_slug' => 'moskva',
                'city_name' => 'Москва',
            ];
        }

        return response()->json([
            'ok' => true,
            'cf_data' => [
                'ip' => $ip,
                'lat' => $lat,
                'lon' => $lon,
            ],
            'cities' => $cities,
            'city' => $cityData,
        ]);
    }
}
