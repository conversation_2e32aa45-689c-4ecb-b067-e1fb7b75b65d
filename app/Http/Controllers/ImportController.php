<?php

namespace App\Http\Controllers;

use App\Models\ImportPostQueue;
use App\Models\Index\IndexGunsBrokerPost;
use App\Models\Index\IndexGunsBrokerUser;
use App\Models\Post;
use App\Services\Parse\GunsbrokerService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Propaganistas\LaravelPhone\PhoneNumber;

class ImportController extends Controller
{
    /**
     * Создать новый импорт
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'phone' => 'required|string',
        ]);

        $phone = new PhoneNumber($request->phone, 'RU');
        $phone = $phone->formatE164();

        $post = IndexGunsBrokerPost::where('user_phone', $phone)->first();
        $user_id = $post?->user_id;

        if (! $user_id) {
            $gbUser = IndexGunsBrokerUser::where('user_phone', $phone)->first();
            $user_id = $gbUser?->user_id;
        }

        if (! $user_id) {
            return response()->json([
                'success' => false,
                'message' => 'No data found for this phone number',
            ], 404);
        }

        $service = new GunsbrokerService;
        $data = $service->parseUserProfile("https://gunsbroker.ru/users/{$user_id}/");

        $exists = 0;

        foreach ($data['posts'] as $draftPost) {
            $exist = Post::where('source', $draftPost)->first();
            if (! $exist) {
                ImportPostQueue::firstOrCreate([
                    'user_name' => $data['name'],
                    'user_avatar' => $data['avatar'],
                    'user_phone' => $phone,
                    'source_name' => 'gunsbroker',
                    'source' => $draftPost,
                ]);
            } else {
                $exists++;
            }
        }

        return response()->json([
            'success' => true,
            'phone' => $phone,
            'posts_count' => count($data['posts']),
            'exists_count' => $exists,
        ], 201);
    }
}
