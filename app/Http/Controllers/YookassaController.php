<?php

namespace App\Http\Controllers;

use App\Models\PaymentWebhook;
use App\Models\Post;
use App\Models\Promotion;
use App\Models\PromotionType;
use App\Services\PromotionService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use YooKassa\Model\Notification\NotificationEventType;
use YooKassa\Model\Notification\NotificationSucceeded;
use YooKassa\Model\Notification\NotificationWaitingForCapture;

class YookassaController extends Controller
{
    public function __construct(
        private PromotionService $promotionService
    ) {}

    public function makePayment(Post $post, Request $request)
    {
        $request->validate([
            'promotion' => 'required|string|in:vip,up,color',
        ]);

        $promotionType = PromotionType::where('type', $request->promotion)->firstOrFail();
        $promotion = $this->promotionService->createPromotion($post, $promotionType);
        $promotionName = $promotion->promotionType->name;

        $description = "Оплата услуги: $promotionName";
        $user = Auth::user();

        $client = new \YooKassa\Client;
        $client->setAuth((int) config('services.yookassa.shop_id'), (string) config('services.yookassa.password'));

        try {
            $idempotenceKey = uniqid('', true);
            $response = $client->createPayment([
                'amount' => [
                    'value' => $promotion->price,
                    'currency' => 'RUB',
                ],
                'confirmation' => [
                    'type' => 'embedded',
                    'locale' => 'ru_RU',
                    'return_url' => env('FRONTEND_URL').'/profile',
                ],
                'save_payment_method' => false,
                'capture' => true,
                'description' => $description,
                'metadata' => [
                    'orderNumber' => $promotion->id,
                ],
                'receipt' => [
                    'customer' => [
                        'full_name' => $user->name ?? "Пользователь #{$user->id}",
                        'email' => $user->email ?? null,
                        'phone' => $user->phone ?? null,
                    ],
                    'items' => [
                        [
                            'description' => $description,
                            'quantity' => 1,
                            'amount' => [
                                'value' => $promotion->price,
                                'currency' => 'RUB',
                            ],
                            'vat_code' => 1,
                            'payment_mode' => \YooKassa\Model\Receipt\PaymentMode::FULL_PREPAYMENT,
                            'payment_subject' => \YooKassa\Model\Receipt\PaymentSubject::SERVICE,
                        ],
                    ],
                ],
            ],
                $idempotenceKey
            );

            // получаем confirmationUrl для дальнейшего редиректа
            return response()->json([
                'ok' => true,
                'url' => $response,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'ok' => false,
                'error' => $e->getMessage(),
            ], 400);
        }

    }

    public function webHookHandle(Request $request)
    {
        try {
            // Сначала сохраняем данные вебхука в БД как есть
            PaymentWebhook::create([
                'gateway' => 'yookassa',
                'amount' => (int) $request->input('object.amount.value'),
                'invoice_id' => $request->input('object.id'),
                'transaction_id' => $request->input('object.id'),
                'data' => $request->all(), // Явно кодируем в JSON для избежания проблем с типами
            ]);

            // Получаем данные запроса
            $source = $request->getContent();
            $requestBody = json_decode($source, true);

            // Определяем тип уведомления
            if (empty($requestBody['event'])) {
                Log::warning('Empty event in notification', ['body' => $requestBody]);

                return response()->json(['success' => true]); // Возвращаем 200 OK для неподдерживаемых типов
            }

            // Создаем объект уведомления в зависимости от типа события
            if ($requestBody['event'] === NotificationEventType::PAYMENT_SUCCEEDED) {
                $notification = new NotificationSucceeded($requestBody);
            } elseif ($requestBody['event'] === NotificationEventType::PAYMENT_WAITING_FOR_CAPTURE) {
                $notification = new NotificationWaitingForCapture($requestBody);
            } else {
                Log::warning('Unsupported notification type', ['event' => $requestBody['event']]);

                return response()->json(['success' => false]); // Возвращаем 200 OK для неподдерживаемых типов
            }

            // Получаем объект платежа из уведомления
            $payment = $notification->getObject();

            // Обрабатываем только успешные платежи
            if ($requestBody['event'] === NotificationEventType::PAYMENT_SUCCEEDED && ! $payment->paid) {
                Log::info('Payment not marked as paid', ['payment_id' => $payment->id]);

                return response()->json(['success' => false]);
            }

            // Проверяем наличие метаданных с номером заказа
            if (empty($payment->metadata) || ! isset($payment->metadata->orderNumber)) {
                Log::error('Order number not found in payment metadata', ['payment_id' => $payment->id]);

                return response()->json(['success' => false]); // Возвращаем 200 OK, чтобы YooKassa не повторяла запрос
            }

            $promotion = Promotion::find($payment->metadata->orderNumber);

            if (! $promotion) {
                Log::error('Promotion not found', ['orderNumber' => $payment->metadata->orderNumber]);

                return response()->json(['success' => false]); // Возвращаем 200 OK, чтобы YooKassa не повторяла запрос
            }

            // Проверяем, что продвижение еще не оплачено (идемпотентность)
            if ($promotion->purchased_at !== null) {
                Log::info('Promotion already purchased', [
                    'promotion_id' => $promotion->id,
                    'purchased_at' => $promotion->purchased_at,
                ]);

                return response()->json(['success' => false]);
            }

            $user = $promotion->post->user;

            // Проверяем, что сумма соответствует
            if ((int) $payment->amount->value !== (int) $promotion->price) {
                Log::warning('Payment amount mismatch', [
                    'payment_amount' => $payment->amount->value,
                    'promotion_price' => $promotion->price,
                ]);

                return response()->json(['success' => false]); // Возвращаем 200 OK, чтобы YooKassa не повторяла запрос
            }

            // Создаем транзакцию через wallet
            $user->deposit(
                $promotion->price,
                [
                    'id' => $promotion->id,
                    'title' => 'Пополнение баланса для продвижения объявления',
                    'description' => 'Пополнение баланса для продвижения объявления',
                    'payment_id' => $payment->id,
                ],
                true // Создаем подтвержденную транзакцию
            );

            // Обновляем статус продвижения
            $promotion->update([
                'purchased_at' => Carbon::now(),
            ]);

            // Обрабатываем продвижение через сервис (без повторной оплаты)
            $this->promotionService->payForPromotion($promotion);

            Log::info('Payment processed successfully', [
                'promotion_id' => $promotion->id,
                'user_id' => $user->id,
                'payment_id' => $payment->id,
                'amount' => $payment->amount->value,
            ]);

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            Log::error('Error processing YooKassa webhook', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->getContent(),
            ]);

            // Всегда возвращаем 200 OK, чтобы YooKassa не повторяла запрос
            return response()->json(['success' => false]);
        }
    }
}
