<?php

namespace App\Http\Controllers;

use App\Http\Resources\TransactionResource;
use App\Services\RobokassaService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;

class BalanceController extends Controller
{
    public function __construct(private RobokassaService $robokassaService) {}

    public function show(Request $request): AnonymousResourceCollection
    {
        $user = Auth::user();
        $transactions = $user->transactions()->where('confirmed', true)->latest()->paginate();

        return TransactionResource::collection($transactions);
    }

    public function deposit(Request $request): JsonResponse
    {
        $user = Auth::user();
        $amount = preg_replace('/[^0-9]/', '', $request->amount);
        $amount_string = \Lang::choice('{0} рублей |{1} :count рубль |[2,4] :count рубля |[5,20] :count рублей |[21,24] :count рубля |[25,*] :count рублей', $amount, [], 'ru');

        $result = $this->robokassaService->payment(
            $user,
            $amount,
            ['title' => 'Пополнение баланса на '.$amount_string]
        );

        return response()->json([
            'success' => false,
            'message' => 'Перейдите по ссылке для пополнения баланса.',
            'url' => $result['url'] ?? null,
            'amount' => $amount,
        ], 200);
    }

    public function withdraw(Request $request): JsonResponse
    {
        $user = Auth::user();
        $user->withdraw($request->amount);

        return response()->json(['ok' => true, 201]);
    }
}
