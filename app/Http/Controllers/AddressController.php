<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class AddressController extends Controller
{
    public function suggest(Request $request)
    {
        $query = $request->input('query');
        if (empty($query)) {
            return [];
        }

        $tokens = [
            '04b1965e588580f3c6cdf82dcc556a6cdb5ba39d',
            //            '6b0013c429d24f1f3389d8ff5bc7b5908d913080',
            //            '2cd34967db3481dfbeb3c3bffa23072f5fbedcfe',
            //            '7720b9ed2ebe39ef79bccbcf07ce101db47cfcab',
            //            '3fb3ce0aa0eb785c1c9c4a967e78c2ff41454be8',
            //            '8c890d6b252427b1a024b544d3b501fc8a618a8c',
            //            '209b8d5e8bb14b84a183757c77c9502d3bca4220',
            //            '3815df32b28623d15cde2b203d0791cbfa06b5a0',
        ];

        $token = $tokens[array_rand($tokens)];

        $response = Http::withOptions([
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => "Token $token",
            ],
            //            'proxy' => config('services.proxy.url'),
            'verify' => false,
        ])->get("http://suggestions.dadata.ru/suggestions/api/4_1/rs/suggest/address?query={$query}");

        //        return $response->status();
        $data = [];
        if (isset($response->json()['suggestions'])) {
            foreach ($response->json()['suggestions'] as $item) {
                $data[] = [
                    'label' => $item['value'],
                    'value' => $item['value'],
                    'unrestricted_value' => $item['unrestricted_value'],
                    'country' => $item['data']['country'],
                    'area_with_type' => $item['data']['area_with_type'],
                    'region_with_type' => $item['data']['region_with_type'],
                    'settlement_with_type' => $item['data']['settlement_with_type'],
                    'geo_lat' => $item['data']['geo_lat'],
                    'geo_lon' => $item['data']['geo_lon'],
                ];
            }
        }

        return $data;

        return $response->json()['suggestions'] ?? [];
    }
}
