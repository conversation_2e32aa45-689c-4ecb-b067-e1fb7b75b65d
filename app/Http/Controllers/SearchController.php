<?php

namespace App\Http\Controllers;

use App\Http\Resources\Index\IndexPostSearchResource;
use App\Services\SearchService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SearchController extends Controller
{
    private SearchService $searchService;

    /**
     * Конструктор контроллера
     */
    public function __construct(SearchService $searchService)
    {
        $this->searchService = $searchService;
    }

    /**
     * Основной метод поиска
     */
    public function search(Request $request): JsonResponse
    {
        try {
            // Валидация входных данных
            $validated = $request->validate([
                'q' => 'nullable|string|max:255',
                'city_slug' => 'nullable|string|max:100',
                'category' => 'nullable|string|max:100',
            ]);

            // Подготовка фильтров
            $filters = $this->prepareFilters($request);

            // Выполнение поиска через сервис
            $posts = $this->searchService->search($validated['q'], $filters);

            // Возврат результатов
            return $this->successResponse($posts);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->errorResponse('Validation error', 422, $e->errors());
        } catch (\Exception $e) {
            Log::error('Search error: '.$e->getMessage(), [
                'query' => $request->get('q'),
                'filters' => $request->only(['city_slug', 'category']),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse('Search error occurred', 500);
        }
    }

    /**
     * Метод для отладки поиска
     */
    public function searchDebug(Request $request): JsonResponse
    {
        try {
            // Валидация входных данных
            $validated = $request->validate([
                'q' => 'required|string|min:1|max:255',
                'city_slug' => 'nullable|string|max:100',
                'category' => 'nullable|string|max:100',
            ]);

            // Подготовка фильтров
            $filters = $this->prepareFilters($request);

            // Получение отладочной информации
            $debugInfo = $this->searchService->getDebugInfo($validated['q'], $filters);

            return response()->json($debugInfo);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->errorResponse('Validation error', 422, $e->errors());
        } catch (\Exception $e) {
            Log::error('Search debug error: '.$e->getMessage(), [
                'query' => $request->get('q'),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse('Debug error occurred', 500);
        }
    }

    /**
     * Очистить кеш поиска
     */
    public function clearCache(Request $request): JsonResponse
    {
        try {
            // Здесь можно добавить проверку прав доступа
            // $this->authorize('admin');

            // Очистка кеша по паттерну
            \Cache::flush(); // Или более точечная очистка

            return response()->json([
                'ok' => true,
                'message' => 'Search cache cleared successfully',
            ]);

        } catch (\Exception $e) {
            Log::error('Cache clear error: '.$e->getMessage());

            return $this->errorResponse('Failed to clear cache', 500);
        }
    }

    /**
     * Подготовить фильтры из запроса
     */
    private function prepareFilters(Request $request): array
    {
        $filters = [];

        if ($request->has('city_slug') && ! empty($request->city_slug)) {
            $filters['city_slug'] = $request->city_slug;
        }

        if ($request->has('category') && ! empty($request->category)) {
            $filters['category'] = $request->category;
        }

        // Можно добавить дополнительные фильтры
        // if ($request->has('price_min')) {
        //     $filters['price_min'] = (float) $request->price_min;
        // }

        // if ($request->has('price_max')) {
        //     $filters['price_max'] = (float) $request->price_max;
        // }

        return $filters;
    }

    /**
     * Формирование успешного ответа
     *
     * @param  mixed  $posts
     */
    private function successResponse($posts): JsonResponse
    {
        if ($posts->isEmpty()) {
            return response()->json([
                'ok' => true,
                'data' => [],
                'message' => 'No results found',
            ]);
        }

        return IndexPostSearchResource::collection($posts)
            ->additional(['ok' => true])
            ->response();
    }

    /**
     * Формирование ответа с ошибкой
     */
    private function errorResponse(string $message, int $code = 400, array $errors = []): JsonResponse
    {
        $response = [
            'ok' => false,
            'message' => $message,
        ];

        if (! empty($errors)) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $code);
    }
}
