<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCreatePostRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'year' => 'год',
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'nullable|integer|min:0',
            'is_rebate' => 'nullable|boolean',
            'is_trade' => 'nullable|boolean',
            'can_ship' => 'nullable|boolean',
            'registration_type' => 'nullable|string|max:255',
            'year' => 'nullable|numeric|min:1000|max:2024',
            'attributes' => 'nullable|array',
            'attributes.*' => 'nullable|string',
        ];
    }

    public function messages(): array
    {
        return [
            //            'city.exists' => 'Город с таким slug не найден.',
            'photos.*.mimes' => 'Фотографии должны быть в формате jpeg, png, jpg, gif или webp.',
        ];
    }

    public function prepareForValidation(): void
    {
        $this->merge([
            'is_rebate' => $this->castToBoolean($this->input('is_rebate')),
            'is_trade' => $this->castToBoolean($this->input('is_trade')),
            'can_ship' => $this->castToBoolean($this->input('can_ship')),
        ]);
    }

    private function castToBoolean($value): bool
    {
        if ($value === 'null' || $value === null) {
            return false;
        }

        return filter_var($value, FILTER_VALIDATE_BOOLEAN);
    }
}
