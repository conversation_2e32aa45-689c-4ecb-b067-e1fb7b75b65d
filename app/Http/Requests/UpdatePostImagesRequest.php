<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdatePostImagesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'photo' => 'required|file|image|mimes:jpeg,png,webp,jpg|max:20480', // 20 MB
        ];
    }

    /**
     * Кастомные сообщения (опционально).
     */
    public function messages(): array
    {
        return [
            'photo.required' => 'Необходимо загрузить хотя бы одно изображение.',
            'photo.image' => 'Файл :attribute должен быть изображением.',
            'photo.mimes' => 'Изображение :attribute должно быть форматом JPEG, PNG или WebP.',
            'photo.max' => 'Размер файла :attribute не должен превышать 20 МБ.',
        ];
    }

    /**
     * Формируем JSON-ответ при неуспешной валидации.
     */
    protected function failedValidation(Validator $validator)
    {
        $errors = $validator->errors()->all();

        throw new HttpResponseException(
            response()->json([
                'ok' => false,
                'errors' => $errors,
            ], 422)
        );
    }
}
