<?php

namespace App\Http\Requests;

use App\Rules\MatchOldPassword;
use Illuminate\Foundation\Http\FormRequest;

class UpdatePasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'password' => 'пароль',
            'new_password' => 'новый пароль',
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'password' => ['required', new MatchOldPassword], // Проверка старого пароля
            'new_password' => ['required', 'confirmed', 'min:6'],   // Новый пароль
        ];
    }

    public function messages(): array
    {
        return [
            'password.required' => 'Старый пароль обязателен.',
            'new_password.required' => 'Новый пароль обязателен.',
            'new_password.confirmed' => 'Пароли не совпадают.',
            'new_password.min' => 'Пароль должен содержать минимум 8 символов.',
        ];
    }
}
