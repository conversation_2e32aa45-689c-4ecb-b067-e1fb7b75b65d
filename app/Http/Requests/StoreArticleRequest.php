<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreArticleRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Или добавьте проверку прав
    }

    public function rules(): array
    {
        return [
            'category_id' => 'required|exists:article_categories,id',
            'title' => 'required|string|min:5|max:255',
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|json',
            'cover_image' => 'nullable|string|max:500',
            'allow_comments' => 'boolean',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:50'
        ];
    }

    public function messages(): array
    {
        return [
            'category_id.required' => 'Выберите категорию',
            'category_id.exists' => 'Выбранная категория не существует',
            'title.required' => 'Заголовок обязателен',
            'title.min' => 'Заголовок должен быть не менее 5 символов',
            'content.required' => 'Контент статьи обязателен',
            'content.json' => 'Контент должен быть в формате JSON (EditorJS blocks)'
        ];
    }
}
