<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PathSeoDataRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'path' => 'required|unique:path_seo_data,path',
            'title' => 'required',
            'meta_description' => 'required|max:255',
            'meta_keywords' => 'max:255',
            'h1' => 'required|max:255',
            'content' => 'max:2500',
        ];
    }
}
