<?php

namespace App\Listeners;

use App\Events\PostView;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades;

class PostViewHandle implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PostView $event): void
    {
        $post_slug = $event->post_slug;
        $user_id = $event->user_id;
        $fingerprint = $event->fingerprint;

        $key = (string) "views:$post_slug;$fingerprint;$user_id";

        Facades\Redis::incr($key);
    }
}
