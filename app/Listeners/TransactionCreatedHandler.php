<?php

namespace App\Listeners;

use App\Models\User;
use App\Notifications\UserDeposit;
use Bavix\Wallet\Internal\Events\TransactionCreatedEventInterface;
use Bavix\Wallet\Models\Transaction;
use Bavix\Wallet\Models\Wallet;

class TransactionCreatedHandler
{
    /**
     * Handle the event.
     */
    public function handle(TransactionCreatedEventInterface $event): void
    {
        $wallet = Wallet::find($event->getWalletId());
        $transaction = Transaction::find($event->getId());
        if (! $transaction->confirmed) {
            return;
        }
        if ($transaction->type !== 'deposit') {
            return;
        }
        $user = $wallet->holder;
        if ($user instanceof User) {
            $user->notify(new UserDeposit($transaction));
        }
    }
}
