<?php

namespace App\Listeners;

use App\Events\ImportPostQueueCreated;
use App\Jobs\ProcessImportPostQueueJob;
use Illuminate\Support\Facades\Log;

class ProcessImportPostQueueListener
{
    public function handle(ImportPostQueueCreated $event): void
    {
        Log::info("Запуск обработки ImportPostQueue ID: {$event->importPostQueue->id}");
        
        ProcessImportPostQueueJob::dispatch($event->importPostQueue);
    }
}
