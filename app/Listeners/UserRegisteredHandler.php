<?php

namespace App\Listeners;

use App\Events\UserRegisteredEvent;
use App\Models\ConversationMessage;
use App\Models\ConversationThread;
use App\Models\User;

class UserRegisteredHandler
{
    /**
     * Handle the event.
     */
    public function handle(UserRegisteredEvent $event): bool
    {
        $user = $event->user;
        $support_user = User::where('email', User::SUPPORT_USER_EMAIL)->first();
        if (! $support_user) {
            return false;
        }

        $thread = ConversationThread::create([
            'subject' => 'Поддержка',
        ]);
        $thread->addParticipant($support_user->id);
        $thread->addParticipant($user->id);

        ConversationMessage::create([
            'thread_id' => $thread->id,
            'user_id' => $support_user->id,
            'body' => 'Добро пожаловать на GunPost.ru!',
        ]);

        $user->support_chat_id = $thread->id;
        $user->save();

        return true;
    }
}
