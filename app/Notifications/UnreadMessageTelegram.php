<?php

namespace App\Notifications;

use App\Models\ConversationMessage;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;
use NotificationChannels\Telegram\TelegramMessage;

class UnreadMessageTelegram extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        protected ConversationMessage $message,
        protected User $sender,
        protected ?int $unreadCount = null,
        protected ?int $threadCount = null
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['telegram'];
    }

    /**
     * Get the Telegram representation of the notification.
     *
     * @return \NotificationChannels\Telegram\TelegramMessage
     */
    public function toTelegram(object $notifiable)
    {
        $threadSubject = $this->message->thread->subject;
        $senderName = $this->sender->name;
        $messagePreview = mb_substr($this->message->body, 0, 100).(mb_strlen($this->message->body) > 100 ? '...' : '');

        // Если это агрегированное уведомление о нескольких сообщениях
        if ($this->unreadCount !== null && $this->unreadCount > 1) {
            // Формируем правильные формы слов для чатов и сообщений
            $chatWord = Lang::choice('чате|чатах|чатах', $this->threadCount, [], 'ru');
            $threadText = "{$this->threadCount} {$chatWord}";
            $messageText = $this->unreadCount.' '.Lang::choice('непрочитанное сообщение|непрочитанных сообщения|непрочитанных сообщений', $this->unreadCount, [], 'ru');

            return TelegramMessage::create()
                ->to($notifiable->telegram->chat_id)
                ->content("*У вас {$messageText} в {$threadText}*\n\nПоследнее сообщение от: {$senderName}\nТема: {$threadSubject}\n\n{$messagePreview}")
                ->button('Открыть чаты', 'https://gunpost.ru/inbox');
        }

        // Если это уведомление об одном сообщении
        return TelegramMessage::create()
            ->to($notifiable->telegram->chat_id)
            ->content("*У вас непрочитанное сообщение*\n\nОт: {$senderName}\nТема: {$threadSubject}\n\n{$messagePreview}")
            ->button('Открыть чаты', 'https://gunpost.ru/inbox');
    }
}
