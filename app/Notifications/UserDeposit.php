<?php

namespace App\Notifications;

use Bavix\Wallet\Models\Transaction;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;

class UserDeposit extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        private Transaction $transaction
    ) {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        $amount = Lang::choice('{0} рублей |{1} :count рубль |[2,4] :count рубля |[5,20] :count рублей |[21,24] :count рубля |[25,*] :count рублей', $this->transaction->amount, [], 'ru');

        return [
            'icon' => '💳',
            'to' => '/wallet',
            'title' => 'Пополнение баланса',
            'message' => "Ваш баланс пополнен на {$amount}",
        ];
    }
}
