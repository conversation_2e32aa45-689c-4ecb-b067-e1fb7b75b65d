<?php

namespace App\Notifications;

use App\Models\Index\IndexPost;
use App\Models\Post;
use App\Models\Report;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\Telegram\TelegramFile;

class PostReport extends Notification implements ShouldQueue
{
    use Queueable;

    public Post $post;

    public Report $report;

    /**
     * Create a new notification instance.
     */
    public function __construct(IndexPost $post, Report $report)
    {
        $this->post = Post::withoutGlobalScopes()->where('slug', $post->slug)->firstOrFail();
        $this->report = $report;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['telegram'];
    }

    /**
     * Get the Telegram representation of the notification.
     */
    public function toTelegram(object $notifiable): TelegramFile
    {
        $this->post->load(['category', 'user']);
        $postUrl = "https://gunpost.ru/{$this->post->category->slug}/{$this->post->slug}.html?preview=true";
        $photo_url = $this->post->getFirstMedia('images')?->getUrl(conversion: '350', fallback: true);

        // Составляем текст сообщения
        $content = "📝 *Новая жалоба на объявление*\n".
            "Заголовок: *{$this->post->title}*\n".
            "Категория: {$this->post->category->name}\n".
            "Цена: {$this->post->price} ₽\n\n";

        if ($this->report?->user) {
            $content .= "\n*Пожаловался пользователь*: {$this->report->user->phone}";
        } else {
            $content .= "\n*Пожаловался пользователь*: {$this->report->email}";
        }

        $content .= "\n\nСообщение: {$this->report->message}";

        // Формируем сообщение
        return TelegramFile::create()
            ->to(config('services.telegram-bot-api.service_chat_id'))
            ->content($content)
            ->photo($photo_url)
            ->button('Посмотреть', $postUrl)
            ->buttonWithCallback('❌ Заблокировать', "ban_post {$this->post->id}");
    }
}
