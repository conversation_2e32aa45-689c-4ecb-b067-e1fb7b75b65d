<?php

namespace App\Notifications;

use App\Models\Post;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\Telegram\TelegramFile;

class PostApproved extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public Post $post
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['telegram'];
    }

    /**
     * Get the Telegram representation of the notification.
     */
    public function toTelegram(object $notifiable): TelegramFile
    {
        $this->post->load(['category', 'user']);
        $postUrl = "https://gunpost.ru/{$this->post->category->slug}/{$this->post->slug}.html?preview=true";
        $photo_url = $this->post->getFirstMedia('images')?->getUrl(conversion: '350', fallback: true);

        // Составляем текст сообщения
        $content = "🔔 *Новое объявление на модерацию*\n".
            "Заголовок: *{$this->post->title}*\n".
            "Категория: {$this->post->category->name}\n".
            "Цена: {$this->post->price} ₽";

        // Добавляем информацию о пользователе, если есть
        if ($this->post->user) {
            $content .= "\nПользователь: {$this->post->user->name}";
            $content .= "\nТелефон: {$this->post->user->phone}";
        }

        // Формируем сообщение
        return TelegramFile::create()
            ->to(config('services.telegram-bot-api.service_chat_id'))
            ->content($content)
            ->photo($photo_url)
            ->button('Посмотреть', $postUrl)
            ->buttonWithCallback('✅ Одобрить', "approve_post {$this->post->id}")
            ->buttonWithCallback('❌ Заблокировать', "ban_post {$this->post->id}");
    }
}
