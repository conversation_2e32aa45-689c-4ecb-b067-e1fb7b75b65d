<?php

namespace App\Jobs;

use App\Events\PostChangeEvent;
use App\Models\ImportPostQueue;
use App\Models\Index\IndexGunsBrokerPost;
use App\Models\Post;
use App\Models\RefModeration;
use App\Models\User;
use App\Services\Parse\GunsbrokerService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessImportPostQueueJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;
    public int $timeout = 30 * 60; // 30 минут
    public int $backoff = 5 * 60; // 5 минут между попытками

    public function __construct(
        public ImportPostQueue $importPostQueue
    ) {
        $this->onQueue('import');
    }

    /**
     * @throws ConnectionException
     */
    public function handle(GunsbrokerService $service): void
    {
        Log::info("Обработка ImportPostQueue ID: {$this->importPostQueue->id}, статус: {$this->importPostQueue->status}");

        // Увеличиваем счетчик попыток
        $this->importPostQueue->increment('attempts');

        try {
            match ($this->importPostQueue->status) {
                'draft' => $this->processDraft($service),
                'process' => $this->processProcess($service),
                'photos' => $this->processPhotos($service),
                'completed' => Log::info("ImportPostQueue ID: {$this->importPostQueue->id} уже завершен"),
                default => throw new \InvalidArgumentException("Неизвестный статус: {$this->importPostQueue->status}")
            };
        } catch (\Throwable $e) {
            Log::error("Ошибка обработки ImportPostQueue ID: {$this->importPostQueue->id}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Сохраняем информацию об ошибке
            $this->importPostQueue->update([
                'error_message' => $e->getMessage(),
                'failed_at' => now()
            ]);

            throw $e;
        }
    }

    /**
     * @throws ConnectionException
     */
    private function processDraft(GunsbrokerService $service): void
    {
        $user = User::where('phone', $this->importPostQueue->user_phone)->first();

        if (!$user) {
            $user = User::create([
                'name' => $this->importPostQueue->user_name,
                'phone' => $this->importPostQueue->user_phone,
            ]);
        }

        $service->parsePost($this->importPostQueue->source, $user->id);

        $this->importPostQueue->update([
            'status' => 'process',
            'process' => 10
        ]);

        // Запускаем следующий этап
        self::dispatch($this->importPostQueue->fresh());
    }

    private function processProcess(GunsbrokerService $service): void
    {
        $post = Post::withoutGlobalScopes()->where('source', $this->importPostQueue->source)->first();
        $user = User::where('phone', $this->importPostQueue->user_phone)->firstOrFail();

        if ($post) {
            Log::info("Объявление уже существует: {$this->importPostQueue->source}");
        } else {
            $product = IndexGunsBrokerPost::where('source', $this->importPostQueue->source)->firstOrFail();
            $post = $service->savePost($product, $user->id);
            Log::info("Обработано объявление: {$post->source}");
        }

        $this->importPostQueue->update([
            'status' => 'photos',
            'process' => 20
        ]);

        // Запускаем следующий этап
        self::dispatch($this->importPostQueue->fresh());
    }

    private function processPhotos(GunsbrokerService $service): void
    {
        Log::info("Получаем фото для: {$this->importPostQueue->source}");

        $product = IndexGunsBrokerPost::where('source', $this->importPostQueue->source)->first();
        if (!$product) {
            Log::warning('Не найден source объявление, удаляем ImportPostQueue');
            $this->importPostQueue->delete();
            return;
        }

        // Обрабатываем удаление ватермарков
        $service->processRemoveWatermark($product);

        // Обновляем прогресс
        $this->updatePhotosProgress();
    }

    private function updatePhotosProgress(): void
    {
        // Пересчитываем продукт и фото после обработки
        $product = IndexGunsBrokerPost::where('source', $this->importPostQueue->source)->firstOrFail();
        $photos = $product->photos ?? [];
        $total = count($photos);
        $processedCount = 0;

        foreach ($photos as $photo) {
            // Считаем «обработанные» фото (URL без 'gunsbroker.ru')
            if (!str_contains($photo['url'], 'gunsbroker.ru')) {
                $processedCount++;
            }
        }

        if ($total > 0) {
            // Переносим прогресс из диапазона 0–100 в 20–100:
            // при 0 фото — process = 20, при all обработанных — process = 100
            $increment = (int) ceil($processedCount * 80 / $total);
            $newProcess = min(100, 20 + $increment);

            // Сохраняем прогресс в очередь
            $this->importPostQueue->update(['process' => $newProcess]);
        } else {
            $newProcess = 100;
        }

        if ($newProcess === 100 || $total === 0) {
            $this->completeProcessing();
        } else {
            Log::info("Прогресс: {$newProcess}% - {$product->source}");

            // Если не завершено, запускаем повторную обработку через некоторое время
            self::dispatch($this->importPostQueue->fresh())->delay(now()->addMinutes(1));
        }
    }

    private function completeProcessing(): void
    {
        $this->importPostQueue->update(['status' => 'completed']);

        $post = Post::withoutGlobalScopes()->where('source', $this->importPostQueue->source)->firstOrFail();
        $post->update([
            'moderation_id' => RefModeration::IS_APPROVED,
            'published_at' => Carbon::now()
        ]);

        PostChangeEvent::dispatch($post);

        Log::info("Завершено: {$post->category->slug}/{$post->slug}");
    }

    public function failed(\Throwable $exception): void
    {
        Log::error("Джоба ProcessImportPostQueueJob провалилась для ImportPostQueue ID: {$this->importPostQueue->id}", [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);

        // Обновляем запись с финальной информацией об ошибке
        $this->importPostQueue->update([
            'error_message' => $exception->getMessage(),
            'failed_at' => now()
        ]);

        // Можно добавить дополнительную логику обработки ошибок
        // Например, отправить уведомление администратору
    }
}
