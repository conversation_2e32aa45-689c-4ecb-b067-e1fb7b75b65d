<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('posts', function (Blueprint $table) {
            $table->id();
            $table->string('title')->nullable();
            $table->unsignedInteger('price')->default(0);
            $table->string('slug')->nullable()->unique();
            $table->integer('year')->nullable();
            $table->foreignIdFor(\App\Models\Category::class);
            $table->text('description')->nullable();
            $table->foreignIdFor(\App\Models\User::class)->nullable();
            $table->foreignIdFor(\App\Models\RefCity::class)->nullable();
            $table->foreignIdFor(\App\Models\GeoItem::class)->nullable();
            $table->integer('views')->default(0);
            $table->integer('favorites')->default(0);
            $table->string('source')->unique()->nullable();
            $table->boolean('is_rebate')->default(false);
            $table->boolean('is_trade')->default(false);
            $table->boolean('can_ship')->default(false);
            $table->string('fingerprint')->nullable();

            $table->string('address')->nullable();
            $table->string('address_geo')->nullable();

            $table->string('registration_type')->nullable();
            $table->string('registration_address')->nullable();
            $table->string('registration_geo')->nullable();

            $table->dateTime('published_at')->nullable();
            $table->dateTime('archived_at')->nullable();
            $table->string('archived_comment')->nullable();
            $table->foreignIdFor(\App\Models\PostArchivedReason::class)->nullable();

            $table->foreignIdFor(\App\Models\RefModeration::class, 'moderation_id')
                ->default(\App\Models\RefModeration::IS_NOT_APPROVED);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('posts');
    }
};
