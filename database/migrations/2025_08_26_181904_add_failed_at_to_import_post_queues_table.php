<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('import_post_queues', function (Blueprint $table) {
            $table->timestamp('failed_at')->nullable()->after('process');
            $table->text('error_message')->nullable()->after('failed_at');
            $table->integer('attempts')->default(0)->after('error_message');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('import_post_queues', function (Blueprint $table) {
            $table->dropColumn(['failed_at', 'error_message', 'attempts']);
        });
    }
};
