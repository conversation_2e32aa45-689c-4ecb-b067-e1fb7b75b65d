<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('path_seo_data', function (Blueprint $table) {
            $table->id();
            $table->string('path');
            $table->string('title');
            $table->string('meta_description');
            $table->string('meta_keywords')->nullable();
            $table->string('h1');
            $table->text('content')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('path_seo_data');
    }
};
