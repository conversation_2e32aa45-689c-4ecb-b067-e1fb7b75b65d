<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Сначала удалим дубликаты, иначе уникальный индекс не создастся
        DB::statement('
            DELETE FROM import_post_queues a
            USING import_post_queues b
            WHERE a.ctid < b.ctid
              AND a.source = b.source
        ');

        Schema::table('import_post_queues', function (Blueprint $table) {
            $table->unique('source', 'import_post_queues_source_unique');
        });
    }

    public function down(): void
    {
        Schema::table('import_post_queues', function (Blueprint $table) {
            $table->dropUnique('import_post_queues_source_unique');
        });
    }
};
