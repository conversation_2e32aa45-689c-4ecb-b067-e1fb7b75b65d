<?php

use Illuminate\Database\Migrations\Migration;
use PDPhil<PERSON>\Elasticsearch\Schema\Blueprint;
use PDPhil<PERSON>\Elasticsearch\Schema\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::deleteIfExists('gunpost_addresses');
        Schema::createIfNotExists('gunpost_addresses', function (Blueprint $index) {
            // Флаг, обозначающий, является ли метка заглавной (например, для столицы)
            $index->keyword('capital_marker');
            // Название города (анализируемый текст)
            $index->text('city');
            // Идентификаторы по ФИАС и КЛАДР для города
            $index->keyword('city_fias_id');
            $index->keyword('city_kladr_id');
            // Тип города
            $index->text('city_type');
            $index->text('city_type_full');
            $index->text('city_with_type');
            // Страна и её ISO код
            $index->text('country');
            $index->keyword('country_iso_code');
            // Дата создания записи
            $index->date('created_at');
            // Федеральный округ
            $index->text('federal_district');
            // Статус актуальности ФИАС, код ФИАС, ID ФИАС и уровень ФИАС
            $index->keyword('fias_actuality_state');
            $index->keyword('fias_code');
            $index->keyword('fias_id');
            $index->keyword('fias_level');
            // Геокоординаты (используем тип double для числового представления)
            $index->double('geo_lat');
            $index->double('geo_lon');
            // Дополнительные идентификаторы
            $index->keyword('kladr_id');
            $index->keyword('okato');
            $index->keyword('oktmo');
            // Почтовый индекс
            $index->keyword('postal_code');
            // Полевые коды контроля качества
            $index->integer('qc');
            $index->integer('qc_complete');
            $index->integer('qc_geo');
            $index->integer('qc_house');
            // Региональные данные
            $index->text('region');
            $index->keyword('region_fias_id');
            $index->keyword('region_iso_code');
            $index->keyword('region_kladr_id');
            $index->text('region_type');
            $index->text('region_type_full');
            $index->text('region_with_type');
            // Результат геокодирования или адресации
            $index->text('result');
            // Источник данных
            $index->keyword('source');
            // Налоговые отделы
            $index->keyword('tax_office');
            $index->keyword('tax_office_legal');
            // Часовой пояс
            $index->text('timezone');
            // Дата обновления записи
            $index->date('updated_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::deleteIfExists('gunpost_addresses');
    }
};
