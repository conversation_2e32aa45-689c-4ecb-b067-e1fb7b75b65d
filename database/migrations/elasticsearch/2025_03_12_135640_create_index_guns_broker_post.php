<?php

use Illuminate\Database\Migrations\Migration;
use PDPhilip\Elasticsearch\Schema\Blueprint;
use PDPhil<PERSON>\Elasticsearch\Schema\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::deleteIfExists('gunpost_guns_broker_posts');
        Schema::createIfNotExists('gunpost_guns_broker_posts', function (Blueprint $index) {
            $index->keyword('source');
            $index->text('title');
            $index->text('address');
            $index->keyword('address_id');
            $index->integer('price');
            $index->boolean('is_rebate');
            $index->boolean('is_trade');
            $index->keyword('user_id');
            $index->text('user_name');
            $index->text('user_avatar');
            $index->keyword('user_phone');
            $index->text('description');
            $index->integer('db_user_id');
            $index->nested('photos')->properties(function (Blueprint $nested) {
                $nested->keyword('url');
                $nested->keyword('filename');
                $nested->boolean('is_processed');
            });

            // Даты
            $index->date('created_at');
            $index->date('updated_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::deleteIfExists('gunpost_guns_broker_posts');
    }
};
