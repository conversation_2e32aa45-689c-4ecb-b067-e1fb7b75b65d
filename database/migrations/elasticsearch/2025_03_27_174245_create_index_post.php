<?php

use Illuminate\Database\Migrations\Migration;
use PDPhilip\Elasticsearch\Schema\Blueprint;
use PDPhilip\Elasticsearch\Schema\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::deleteIfExists('gunpost_posts');
        Schema::createIfNotExists('gunpost_posts', function (Blueprint $index) {
            $index->keyword('id');
            $index->text('title');
            $index->text('description');
            $index->keyword('slug');
            $index->integer('price');
            $index->integer('year');

            $index->keyword('category_id');
            $index->keyword('type');
            $index->keyword('user_id');
            $index->keyword('city_id');
            $index->keyword('geo_item_id');
            $index->keyword('archived_reason');
            $index->keyword('archived_reason_id');
            $index->keyword('moderation_id');

            $index->integer('views');
            $index->integer('favorites');

            $index->keyword('source');

            $index->boolean('is_rebate');
            $index->boolean('is_trade');
            $index->boolean('can_ship');

            $index->keyword('fingerprint');

            $index->text('address');
            $index->geo('address_geo');

            $index->keyword('registration_type');
            $index->text('registration_address');
            $index->geo('registration_geo');

            $index->date('published_at');
            $index->date('archived_at');
            $index->text('archived_comment');

            $index->date('created_at');
            $index->date('updated_at');
            $index->date('promotion_start_date');

            $index->nested('attributes')->properties(function (Blueprint $nested) {
                $nested->keyword('type'); // conditions / calibers / gun_types и т.п.
                $nested->keyword('name');
                $nested->keyword('slug');
            });

            $index->keyword('seller_name');
            $index->keyword('seller_avatar');

            $index->nested('images')->properties(function (Blueprint $nested) {
                $nested->long('height');
                $nested->long('width');
                $nested->keyword('src');
                $nested->nested('srcSet')->properties(function (Blueprint $nested) {
                    $nested->long('height');
                    $nested->long('width');
                    $nested->keyword('src');
                });
            });

            $index->keyword('thumbnails');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::deleteIfExists('gunpost_posts');
    }
};
