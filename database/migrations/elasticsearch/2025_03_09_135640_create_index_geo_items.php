<?php

use Illuminate\Database\Migrations\Migration;
use PDPhilip\Elasticsearch\Schema\Blueprint;
use PDPhilip\Elasticsearch\Schema\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::deleteIfExists('gunpost_geo_items');
        Schema::createIfNotExists('gunpost_geo_items', function (Blueprint $index) {
            $index->text('title');
            $index->text('address');
            $index->keyword('type');
            $index->keyword('phones');

            // Гео-точка (как массив из lat/lng)
            $index->geo('point');

            // Даты
            $index->date('created_at');
            $index->date('updated_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::deleteIfExists('gunpost_geo_items');
    }
};
