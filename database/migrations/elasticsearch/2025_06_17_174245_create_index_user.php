<?php

use Illuminate\Database\Migrations\Migration;
use PDPhilip\Elasticsearch\Schema\Blueprint;
use PDPhilip\Elasticsearch\Schema\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::deleteIfExists('gunpost_guns_broker_users');
        Schema::createIfNotExists('gunpost_guns_broker_users', function (Blueprint $index) {
            $index->keyword('type');
            $index->keyword('user_id');
            $index->keyword('user_name');
            $index->keyword('user_phone');
            $index->keyword('user_avatar');
            $index->date('last_activity_at');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::deleteIfExists('gunpost_guns_broker_users');
    }
};
