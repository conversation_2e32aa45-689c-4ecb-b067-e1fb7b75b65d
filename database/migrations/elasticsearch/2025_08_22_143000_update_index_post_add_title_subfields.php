<?php

use Elastic\Elasticsearch\ClientBuilder;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    private string $index = 'gunpost_posts';

    public function up(): void
    {
        $client = $this->makeClient();

        // 1) Закрываем индекс (нужно для обновления analysis)
        $client->indices()->close(['index' => $this->index]);

        // 2) Добавляем/обновляем analysis (char_filter + analyzers)
        $client->indices()->putSettings([
            'index' => $this->index,
            'body' => [
                'analysis' => [
                    'char_filter' => [
                        'ru_en_keyboard' => [
                            'type' => 'mapping',
                            'mappings' => [
                                'q => й', 'w => ц', 'e => у', 'r => к', 't => е', 'y => н', 'u => г', 'i => ш', 'o => щ', 'p => з', '[ => х', '] => ъ',
                                'a => ф', 's => ы', 'd => в', 'f => а', 'g => п', 'h => р', 'j => о', 'k => л', 'l => д', '; => ж', "' => э",
                                'z => я', 'x => ч', 'c => с', 'v => м', 'b => и', 'n => т', 'm => ь', ', => б', '. => ю', '/ => .',
                            ],
                        ],
                    ],
                    'filter' => [
                        'edge_ngram_filter' => [
                            'type' => 'edge_ngram',
                            'min_gram' => 2,
                            'max_gram' => 20,
                        ],
                        'trigram_filter' => [
                            'type' => 'ngram',
                            'min_gram' => 3,
                            'max_gram' => 3,
                        ],
                        'russian_stop' => [
                            'type' => 'stop',
                            'stopwords' => '_russian_',
                        ],
                        'russian_stemmer' => [
                            'type' => 'stemmer',
                            'language' => 'russian',
                        ],
                        'russian_keywords' => [
                            'type' => 'keyword_marker',
                            'keywords' => [], // Можно добавить слова, которые не нужно стеммить
                        ],
                    ],
                    'analyzer' => [
                        'edge_text_analyzer' => [
                            'type' => 'custom',
                            'char_filter' => ['ru_en_keyboard'],
                            'tokenizer' => 'standard',
                            'filter' => ['lowercase', 'asciifolding', 'edge_ngram_filter'],
                        ],
                        'standard_keyboard' => [
                            'type' => 'custom',
                            'char_filter' => ['ru_en_keyboard'],
                            'tokenizer' => 'standard',
                            'filter' => ['lowercase', 'asciifolding'],
                        ],
                        'trigram_text_analyzer' => [
                            'type' => 'custom',
                            'char_filter' => ['ru_en_keyboard'],
                            'tokenizer' => 'standard',
                            'filter' => ['lowercase', 'asciifolding', 'trigram_filter'],
                        ],
                        'russian_morphology' => [
                            'type' => 'custom',
                            'char_filter' => ['ru_en_keyboard'],
                            'tokenizer' => 'standard',
                            'filter' => [
                                'lowercase',
                                'russian_stop',
                                'russian_keywords',
                                'russian_stemmer',
                            ],
                        ],
                        'russian_morphology_search' => [
                            'type' => 'custom',
                            'char_filter' => ['ru_en_keyboard'],
                            'tokenizer' => 'standard',
                            'filter' => [
                                'lowercase',
                                'russian_keywords',
                                'russian_stemmer',
                            ],
                        ],
                    ],
                ],
            ],
        ]);

        // 3) Открываем индекс (после обновления analysis)
        $client->indices()->open(['index' => $this->index]);

        // 4) Обновляем mapping: добавляем под-поля для title и description
        $client->indices()->putMapping([
            'index' => $this->index,
            'body' => [
                'properties' => [
                    'title' => [
                        'type' => 'text',
                        'fields' => [
                            'edge' => [
                                'type' => 'text',
                                'analyzer' => 'edge_text_analyzer',
                                'search_analyzer' => 'standard_keyboard',
                            ],
                            'trigram' => [
                                'type' => 'text',
                                'analyzer' => 'trigram_text_analyzer',
                                'search_analyzer' => 'standard_keyboard',
                            ],
                            'keyboard' => [
                                'type' => 'text',
                                'analyzer' => 'standard_keyboard',
                                'search_analyzer' => 'standard_keyboard',
                            ],
                            'stemmed' => [
                                'type' => 'text',
                                'analyzer' => 'russian_morphology',
                                'search_analyzer' => 'russian_morphology_search',
                            ],
                        ],
                    ],
                    'description' => [
                        'type' => 'text',
                        'fields' => [
                            'edge' => [
                                'type' => 'text',
                                'analyzer' => 'edge_text_analyzer',
                                'search_analyzer' => 'standard_keyboard',
                            ],
                            'trigram' => [
                                'type' => 'text',
                                'analyzer' => 'trigram_text_analyzer',
                                'search_analyzer' => 'standard_keyboard',
                            ],
                            'keyboard' => [
                                'type' => 'text',
                                'analyzer' => 'standard_keyboard',
                                'search_analyzer' => 'standard_keyboard',
                            ],
                            'stemmed' => [
                                'type' => 'text',
                                'analyzer' => 'russian_morphology',
                                'search_analyzer' => 'russian_morphology_search',
                            ],
                        ],
                    ],
                ],
            ],
        ]);

        // 5) Переиндексируем документы, чтобы заполнились новые под-поля (update_by_query)
        $client->updateByQuery([
            'index' => $this->index,
            'conflicts' => 'proceed',
            'refresh' => true,
            'body' => [
                'script' => [
                    // пустой script, важен сам reindex-триггер; painless по умолчанию
                    'source' => 'ctx._source = ctx._source',
                    'lang' => 'painless',
                ],
                'query' => [
                    'match_all' => (object) [],
                ],
            ],
        ]);
    }

    public function down(): void
    {
        // Невозможно безопасно удалить под-поля/анализаторы из существующего индекса без его пересоздания.
        // Оставляем как есть.
    }

    private function makeClient()
    {
        $host = env('ES_EXPLORER_HOST', 'localhost');
        $port = env('ES_EXPLORER_PORT', 9200);
        $scheme = env('ES_EXPLORER_SCHEME', 'http');
        $username = env('ES_USERNAME');
        $password = env('ES_PASSWORD');

        $builder = ClientBuilder::create()->setHosts([sprintf('%s://%s:%s', $scheme, $host, $port)]);
        if ($username || $password) {
            $builder->setBasicAuthentication((string) $username, (string) $password);
        }

        return $builder->build();
    }
};
