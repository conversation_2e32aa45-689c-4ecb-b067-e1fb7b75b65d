<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('import_post_queues', function (Blueprint $table) {
            $table->id();
            $table->string('source_name');
            $table->string('source');
            $table->string('user_name');
            $table->string('user_phone');
            $table->string('user_avatar')->nullable();
            $table->enum('status', ['draft', 'process', 'photos', 'completed'])->default('draft');
            $table->integer('process')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('import_post_queues');
    }
};
