<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('text_ru_checks', function (Blueprint $table) {
            $table->id();
            $table->morphs('checkable');
            $table->string('text_ru_uid')->nullable(); // UID текста, полученный от text.ru
            $table->string('status')->default('pending'); // Статус проверки

            $table->json('result')->nullable(); // Результат проверки
            $table->json('spell_check')->nullable();
            $table->json('seo_check')->nullable();

            $table->double('text_unique')->nullable(); // Процент уникальности
            $table->string('error_code')->nullable();
            $table->string('error_desc')->nullable();
            $table->timestamp('date_check')->nullable();

            $table->timestamps();

            $table->index('text_ru_uid');
            $table->unique(['checkable_type', 'checkable_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('text_ru_checks');
    }
};
