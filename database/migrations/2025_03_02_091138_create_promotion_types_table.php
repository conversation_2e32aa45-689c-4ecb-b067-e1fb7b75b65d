<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('promotion_types', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Название типа продвижения (например, "Поднятие", "Аукцион")
            $table->string('type')->unique(); // Уникальный идентификатор типа (например, "upper", "auction")
            $table->text('description')->nullable(); // Описание типа продвижения
            $table->string('value')->nullable(); // Дополнительное значение (например, количество баллов для аукциона)
            $table->unsignedInteger('price')->default(0); // Стоимость услуги (в условных единицах)
            $table->unsignedInteger('duration')->default(0); // Длительность эффекта продвижения (в днях)
            $table->softDeletes();
            $table->timestamps();
        });

        /**
         * Поднятие
         * upper
         * ---
         * 100
         * 7
         * value: null
         */
        /**
         * Аукцион
         * auction
         * ---
         * 30
         * 7
         * value: 30 баллов
         */
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('promotion_types');
    }
};
