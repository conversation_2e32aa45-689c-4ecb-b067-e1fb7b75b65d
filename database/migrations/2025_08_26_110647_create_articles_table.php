<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('articles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('category_id')->constrained('article_categories');

            $table->string('title');
            $table->string('slug')->unique();
            $table->text('excerpt')->nullable();
            $table->json('content'); // EditorJS blocks
            $table->string('cover_image')->nullable();

            $table->enum('status', ['draft', 'moderation', 'published', 'rejected'])
                ->default('draft');
            $table->string('rejection_reason')->nullable();

            $table->integer('views_count')->default(0);
            $table->integer('read_time')->default(0); // в минутах

            $table->boolean('is_featured')->default(false);
            $table->boolean('allow_comments')->default(true);

            $table->timestamp('published_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index('slug');
            $table->index('published_at');
            $table->index('is_featured');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('articles');
    }
};
