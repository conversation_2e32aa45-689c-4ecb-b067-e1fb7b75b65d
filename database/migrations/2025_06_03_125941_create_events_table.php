<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('events', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug');
            $table->string('description');
            $table->string('image_alt')->nullable();
            $table->string('place');
            $table->string('address');
            $table->string('address_geo')->nullable();
            $table->dateTime('start_date');
            $table->dateTime('end_date');
            $table->text('content');
            $table->foreignIdFor(\App\Models\RefCity::class)->nullable();

            $table->enum('status', ['draft', 'published'])->default('draft');
            $table->string('meta_title');
            $table->string('meta_description');
            $table->string('meta_keywords')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('events');
    }
};
