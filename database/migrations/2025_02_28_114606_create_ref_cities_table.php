<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ref_cities', function (Blueprint $table) {
            $table->id();
            $table->integer('order')->default(0);
            $table->string('name');
            $table->string('name_pred')->nullable();
            $table->string('slug')->unique();
            $table->integer('region_number')->unique();

            $table->integer('postal_code')->nullable();
            $table->string('country')->nullable();
            $table->string('country_iso_code')->nullable();
            $table->string('region_iso_code')->nullable();
            $table->string('region_type')->nullable();
            $table->string('region_type_full')->nullable();
            $table->string('region')->nullable();
            $table->string('city_with_type')->nullable();
            $table->string('city_type')->nullable();
            $table->string('city_type_full')->nullable();
            $table->string('city')->nullable();

            $table->double('lat')->nullable();
            $table->double('lng')->nullable();

            $table->foreignIdFor(\App\Models\RefCountry::class);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ref_cities');
    }
};
