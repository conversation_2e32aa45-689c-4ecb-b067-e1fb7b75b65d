<?php

namespace Database\Seeders;

use App\Models\RefModeration;
use Illuminate\Database\Seeder;

class ModerationsSeed extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        RefModeration::firstOrCreate([
            'name' => 'Не модерирован',
            'id' => RefModeration::IS_NOT_APPROVED,
        ]);
        RefModeration::firstOrCreate([
            'name' => 'Модерирован',
            'id' => RefModeration::IS_APPROVED,
        ]);
        RefModeration::firstOrCreate([
            'name' => 'Забанен',
            'id' => RefModeration::IS_BANNED,
        ]);
    }
}
