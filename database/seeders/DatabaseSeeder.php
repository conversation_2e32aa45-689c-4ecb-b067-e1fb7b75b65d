<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            SupportUserSeed::class,
            ModerationsSeed::class,
            CountriesSeed::class,
            CitiesSeeder::class,
            ConditionsSeed::class,
            FirearmsSeed::class,
            GunTypeSeed::class,
            VendorsSeed::class,
            ModelsSeed::class,
            CaliberSeed::class,
            CategorySeed::class,
            PostArchivedReasonSeed::class,
            RefGunOrientationSeed::class,
            RefGunReloadingSeed::class,
            PromotionTypeSeed::class,
            BrandSeed::class,
            RefTypeSeed::class,
        ]);
    }
}
