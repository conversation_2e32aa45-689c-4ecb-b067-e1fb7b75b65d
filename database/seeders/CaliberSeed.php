<?php

namespace Database\Seeders;

use App\Models\RefCaliber;
use App\Models\RefGunType;
use App\Models\RefModeration;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class CaliberSeed extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $filePath = public_path('seed/calibers.csv');
        $data = array_map(fn ($row) => str_getcsv($row, ';'), file($filePath));
        $headers = array_shift($data);

        foreach ($data as $row) {
            $item = array_combine($headers, $row);
            $type = RefGunType::where('name', $item['Тип'])->first();

            if (! $type) {
                continue;
            }

            RefCaliber::updateOrCreate([
                'slug' => $type->slug.'-'.Str::slug($item['Калибр']),
            ], [
                'name' => $item['Калибр'],
                'size' => $item['Размер'],
                'ref_gun_type_id' => $type->id,
                'moderation_id' => RefModeration::IS_APPROVED,
            ]);
        }
    }
}
