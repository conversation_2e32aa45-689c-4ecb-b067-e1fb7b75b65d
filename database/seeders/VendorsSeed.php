<?php

namespace Database\Seeders;

use App\Models\RefCountry;
use App\Models\RefModeration;
use App\Models\RefVendor;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class VendorsSeed extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $filePath = public_path('seed/vendors.csv');
        $data = array_map('str_getcsv', file($filePath));
        $headers = array_shift($data);

        foreach ($data as $row) {
            $item = array_combine($headers, $row);

            $country = RefCountry::where('name', $item['Страна'])->first();

            if (! $country) {
                $country = RefCountry::create([
                    'name' => $item['Страна'],
                    'slug' => Str::slug($item['Страна']),
                    'moderation_id' => RefModeration::IS_APPROVED,
                ]);
            }

            RefVendor::upsert([
                'name' => $item['Производитель'],
                'slug' => Str::slug($item['Производитель']),
                'ref_country_id' => $country->id,
                'region' => $item['Регион'],
            ], 'slug');
        }
    }
}
