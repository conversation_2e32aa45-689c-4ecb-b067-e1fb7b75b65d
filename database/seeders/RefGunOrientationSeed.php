<?php

namespace Database\Seeders;

use App\Models\RefGunOrientation;
use App\Models\RefModeration;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class RefGunOrientationSeed extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            'Горизонтальное',
            'Вертикальное',
        ];

        foreach ($data as $item) {
            RefGunOrientation::firstOrCreate([
                'name' => $item,
                'slug' => Str::slug($item),
                'moderation_id' => RefModeration::IS_APPROVED,
            ]);
        }
    }
}
