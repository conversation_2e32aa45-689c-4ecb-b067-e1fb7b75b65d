<?php

namespace Database\Seeders;

use App\Models\PostArchivedReason;
use Illuminate\Database\Seeder;

class PostArchivedReasonSeed extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $reasons = [
            'Продал на Ганпост',
            'Продал где-то еще',
            'Другая причина',
        ];

        foreach ($reasons as $reason) {
            PostArchivedReason::firstOrCreate([
                'name' => $reason,
            ]);
        }
    }
}
