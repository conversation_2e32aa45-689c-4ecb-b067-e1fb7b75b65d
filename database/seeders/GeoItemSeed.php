<?php

namespace Database\Seeders;

use App\Models\GeoItem;
use App\Models\Index\IndexGeoItems;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Propaganistas\LaravelPhone\PhoneNumber;

class GeoItemSeed extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $filePath = public_path('seed/geo.json');

        if (! File::exists($filePath)) {
            throw new \Exception("File not found: {$filePath}");
        }

        $data = json_decode(File::get($filePath), true);

        if (! $data) {
            throw new \Exception("Invalid JSON format in {$filePath}");
        }

        foreach ($data as $item) {
            $coordinates = explode(',', $item['coordinates'] ?? ',');
            $lat = isset($coordinates[1]) ? trim($coordinates[0]) : null;
            $lng = isset($coordinates[0]) ? trim($coordinates[1]) : null;
            $title = preg_replace('/\s+/', ' ', trim($item['title'] ?? ''));

            $phones = [];
            foreach (array_filter($item['phones']) as $phone) {
                $string_phone = preg_replace('/\D/', '', $phone);
                try {
                    $string_phone = new PhoneNumber($string_phone, 'RU');
                    $string_phone = $string_phone->formatE164();
                    $phones[] = $string_phone;
                } catch (\Throwable $e) {

                }
            }

            GeoItem::updateOrCreate(
                ['title' => $title, 'address' => $item['address']],
                [
                    'type' => $item['type'] ?? null,
                    'lat' => $lat,
                    'lng' => $lng,
                    'phones' => $phones,
                ]
            );

            $indexModel = new IndexGeoItems;
            $indexModel->fill([
                '_id' => Str::slug($title),
                'title' => $title,
                'address' => $item['address'],
                'type' => $item['type'] ?? null,
                //                'lat' => $lat,
                //                'lng' => $lng,
                'point' => ['lat' => $lat, 'lon' => $lng],
                'phones' => $phones,
            ]);
            $indexModel->withoutRefresh()->save();
        }
    }
}
