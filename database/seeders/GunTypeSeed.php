<?php

namespace Database\Seeders;

use App\Models\RefGunType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class GunTypeSeed extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $filePath = public_path('seed/calibers.csv');
        //         ADD: Сменный
        $data = array_map(fn ($row) => str_getcsv($row, ';'), file($filePath));
        $headers = array_shift($data);

        foreach ($data as $row) {
            $item = array_combine($headers, $row);

            RefGunType::upsert([
                'name' => $item['Тип'],
                'slug' => Str::slug($item['Тип']),
            ], 'slug');
        }
    }
}
