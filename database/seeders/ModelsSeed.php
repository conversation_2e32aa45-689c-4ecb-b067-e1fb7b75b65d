<?php

namespace Database\Seeders;

use App\Models\RefModel;
use App\Models\RefModeration;
use App\Models\RefVendor;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class ModelsSeed extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $filePath = public_path('seed/models.csv');
        $data = array_map(fn ($row) => str_getcsv($row, ';'), file($filePath));
        $headers = array_shift($data);

        foreach ($data as $row) {
            $item = array_combine($headers, $row);

            $vendor = RefVendor::where('name', 'ilike', '%'.$item['Производитель'].'%')->first();
            if (! $vendor) {
                continue;
            }

            RefModel::updateOrCreate([
                'slug' => $vendor->slug.'-'.Str::slug($item['Модель']),
            ], [
                'name' => $item['Модель'],
                'ref_vendor_id' => $vendor->id,
                'moderation_id' => RefModeration::IS_APPROVED,
            ]);

        }
    }
}
