<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\RefType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;

class RefTypeSeed extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $filePath = public_path('seed/zip_categories.json');

        if (! File::exists($filePath)) {
            throw new \Exception("File not found: {$filePath}");
        }

        $data = json_decode(File::get($filePath), true);

        if (! $data) {
            throw new \Exception("Invalid JSON format in {$filePath}");
        }

        $categoryMap = [
            1 => 'zip',      // Приклады → ЗИП
            3 => 'zip',      // Дульные устройства → ЗИП
            4 => 'zip',
            5 => 'zip',
            6 => 'zip',
            7 => 'zip',
            9 => 'zip',
            10 => 'zip',
            11 => 'zip',
            13 => 'zip',
            14 => 'zip',
            15 => 'zip',
            16 => 'zip',
            17 => 'optika_priceli', // Кронштейны и целики → Оптика
            18 => 'optika_priceli', // Коллиматоры
            21 => 'optika_priceli', // Оптические прицелы
            23 => 'airsoft',
            24 => 'airsoft',
            25 => 'airsoft',
            27 => 'kobury',
            28 => 'kobury',
            29 => 'kobury',
            31 => 'kobury',
            32 => 'kobury',
            33 => 'kobury',
            34 => 'kobury',
            35 => 'kobury',
            36 => 'reloading',
            42 => 'reloading',
            43 => 'reloading',
            46 => 'kobury',
            47 => 'kobury',
            49 => 'zip',
            50 => 'zip',
            51 => 'reloading',
            52 => 'reloading',
            53 => 'reloading',
            55 => 'kobury',
            56 => 'airsoft',
            57 => 'airsoft',
            58 => 'airsoft',
            59 => 'airsoft',
            60 => 'airsoft',
        ];

        foreach ($data as $item) {
            $slug = $categoryMap[$item['id']] ?? null;
            if ($slug) {
                $category = Category::where('slug', $slug)->first();
                try {
                    RefType::firstOrCreate([
                        'name' => $item['name'],
                        'slug' => $item['slug'],
                        'category_id' => $category->id,
                    ]);
                } catch (\Exception $e) {
                    continue;
                }
            }
        }
    }
}
