<?php

namespace Database\Seeders;

use App\Models\RefFirearm;
use App\Models\RefModeration;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class FirearmsSeed extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $filePath = public_path('seed/materials.csv');
        $data = array_map(fn ($row) => str_getcsv($row, ';'), file($filePath));
        $headers = array_shift($data);

        foreach ($data as $row) {
            $item = array_combine($headers, $row);

            RefFirearm::upsert([
                'name' => $item['Материал'],
                'slug' => Str::slug($item['Материал']),
                'moderation_id' => RefModeration::IS_APPROVED,
            ], 'slug');
        }
    }
}
