<?php

namespace Database\Seeders;

use App\Models\RefCity;
use App\Models\RefCountry;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;

class CitiesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $filePath = public_path('seed/cities.json');

        if (! File::exists($filePath)) {
            throw new \Exception("File not found: {$filePath}");
        }

        $data = json_decode(File::get($filePath), true);

        if (! $data) {
            throw new \Exception("Invalid JSON format in {$filePath}");
        }

        $country = RefCountry::firstOrCreate([
            'name' => 'Россия',
            'slug' => 'russia',
        ]);

        foreach ($data as $item) {
            $item['ref_country_id'] = $country->id;

            RefCity::updateOrCreate([
                'region_number' => $item['region_number'],
            ], array_filter($item));
        }
    }
}
