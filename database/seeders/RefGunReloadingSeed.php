<?php

namespace Database\Seeders;

use App\Models\RefGunReloading;
use App\Models\RefModeration;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class RefGunReloadingSeed extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            'Переломное',
            'Помповое',
            'Инерционное',
            'Газоотводное',
            'Продольно-Скользящий',
            'Скоба Генри',
            'Револьверное',
            'Свободный затвор',
            'Дульное зарядное',
        ];

        foreach ($data as $item) {
            RefGunReloading::firstOrCreate([
                'name' => $item,
                'slug' => Str::slug($item),
                'moderation_id' => RefModeration::IS_APPROVED,
            ]);
        }
    }
}
