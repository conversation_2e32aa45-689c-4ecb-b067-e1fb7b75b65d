<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class PromotionTypeSeed extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $types = [
            [
                'name' => 'Поднять в поиске',
                'type' => 'up',
                'description' => 'Поднять объявление в поиске',
                'price' => 100,
            ],
            [
                'name' => 'Выделить объявление цветом',
                'type' => 'color',
                'description' => 'Выделить объявление цветом',
                'price' => 150,
            ],
            [
                'name' => 'Сделать VIP',
                'type' => 'vip',
                'description' => 'Сделать объявление в VIP блоке',
                'price' => 200,
            ],
        ];

        foreach ($types as $type) {
            \App\Models\PromotionType::firstOrCreate($type);
        }
    }
}
