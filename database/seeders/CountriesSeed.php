<?php

namespace Database\Seeders;

use App\Models\RefCountry;
use App\Models\RefModeration;
use Illuminate\Database\Seeder;

class CountriesSeed extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            [
                'name' => 'США',
                'slug' => 'usa',
                'moderation_id' => RefModeration::IS_APPROVED,
            ],
            [
                'name' => 'Италия',
                'slug' => 'italy',
                'moderation_id' => RefModeration::IS_APPROVED,
            ],
            [
                'name' => 'Германия',
                'slug' => 'germany',
                'moderation_id' => RefModeration::IS_APPROVED,
            ],
            [
                'name' => 'Германия/Швейцария',
                'slug' => 'germany-switzerland',
                'moderation_id' => RefModeration::IS_APPROVED,
            ],
            [
                'name' => 'Австрия',
                'slug' => 'austria',
                'moderation_id' => RefModeration::IS_APPROVED,
            ],
            [
                'name' => 'Чехия',
                'slug' => 'czechia',
                'moderation_id' => RefModeration::IS_APPROVED,
            ],
            [
                'name' => 'Финляндия',
                'slug' => 'finland',
                'moderation_id' => RefModeration::IS_APPROVED,
            ],
            [
                'name' => 'Чили',
                'slug' => 'chile',
                'moderation_id' => RefModeration::IS_APPROVED,
            ],
            [
                'name' => 'Россия',
                'slug' => 'russia',
                'moderation_id' => RefModeration::IS_APPROVED,
            ],
            [
                'name' => 'Китай',
                'slug' => 'china',
                'moderation_id' => RefModeration::IS_APPROVED,
            ],
            [
                'name' => 'Япония',
                'slug' => 'japan',
                'moderation_id' => RefModeration::IS_APPROVED,
            ],
            [
                'name' => 'Южная Корея',
                'slug' => 'south-korea',
                'moderation_id' => RefModeration::IS_APPROVED,
            ],
            [
                'name' => 'Израиль',
                'slug' => 'israel',
                'moderation_id' => RefModeration::IS_APPROVED,
            ],
            [
                'name' => 'США/Израиль',
                'slug' => 'usa-israel',
                'moderation_id' => RefModeration::IS_APPROVED,
            ],
            [
                'name' => 'Южная Африка',
                'slug' => 'south-africa',
                'moderation_id' => RefModeration::IS_APPROVED,
            ],
            [
                'name' => 'Бельгия',
                'slug' => 'belgium',
                'moderation_id' => RefModeration::IS_APPROVED,
            ],
            [
                'name' => 'Австралия',
                'slug' => 'australia',
                'moderation_id' => RefModeration::IS_APPROVED,
            ],
        ];

        foreach ($data as $item) {
            RefCountry::upsert($item, 'slug');
        }
    }
}
