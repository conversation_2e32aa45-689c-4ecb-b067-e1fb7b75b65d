<?php

namespace Database\Seeders;

use App\Models\RefCondition;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class ConditionsSeed extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = ['Новое', 'Идеальное', 'Хорошее', 'Среднее', 'Требует ремонта'];

        foreach ($data as $item) {
            RefCondition::upsert([
                'name' => $item,
                'slug' => Str::slug($item),
            ], 'slug');
        }
    }
}
