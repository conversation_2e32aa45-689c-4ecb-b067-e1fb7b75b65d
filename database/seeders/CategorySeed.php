<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;

class CategorySeed extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            ['registration' => true, 'category' => 'hunting', 'label' => 'Охотничье оружие', 'image' => '/images/cat-1.png', 'colSpan' => 'col-span-6 md:col-span-4'],
            ['registration' => true, 'category' => 'selfdefence', 'label' => 'Оружие самообороны', 'image' => '/images/cat-2.png', 'colSpan' => 'col-span-6 md:col-span-4'],
            ['category' => 'pnevma', 'label' => 'Пневматика', 'image' => '/images/cat-3.png', 'colSpan' => 'col-span-5 md:col-span-3'],
            ['category' => 'optika_priceli', 'label' => 'Оптика', 'image' => '/images/cat-4.png', 'colSpan' => 'col-span-6 md:col-span-3'],
            ['category' => 'knife', 'label' => 'Ножи и клинки', 'image' => '/images/cat-7.png', 'colSpan' => 'col-span-6 md:col-span-3'],
            ['category' => 'zip', 'label' => 'ЗИП', 'image' => '/images/cat-5.png', 'colSpan' => 'col-span-5 md:col-span-3'],
            ['category' => 'safes', 'label' => 'Сейфы', 'image' => '/images/cat-6.png', 'colSpan' => 'col-span-8 md:col-span-3'],
            ['category' => 'kobury', 'label' => 'Сумки, Кобуры', 'image' => '/images/cat-8.png', 'colSpan' => 'col-span-9 md:col-span-3'],
            ['category' => 'reloading', 'label' => 'Релоадинг и оборудование', 'image' => '/images/cat-9.png', 'colSpan' => 'col-span-9 md:col-span-4'],
            ['category' => 'airsoft', 'label' => 'Страйкбол', 'image' => '/images/cat-10.png', 'colSpan' => 'col-span-8 md:col-span-4'],
        ];

        foreach ($data as $item) {
            $category = Category::firstOrCreate([
                'name' => $item['label'],
                'slug' => $item['category'],
                'registration' => $item['registration'] ?? false,
            ]);

            $category->icon = $item['image'];
            $category->save();
        }
    }
}
