<?php

namespace Database\Seeders;

use App\Models\RefBrand;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class BrandSeed extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $filePath = public_path('seed/brands.json');

        if (! File::exists($filePath)) {
            throw new \Exception("File not found: {$filePath}");
        }

        $data = json_decode(File::get($filePath), true);

        if (! $data) {
            throw new \Exception("Invalid JSON format in {$filePath}");
        }

        foreach ($data as $item) {
            if (preg_match('#^/shop/brand/([^/]+)/?#', $item['url'], $matches)) {
                $brandSlug = $matches[1];
            }
            RefBrand::firstOrCreate([
                'name' => $item['name'],
                'slug' => $brandSlug ?? Str::slug($item['name']),
            ]);
        }
    }
}
