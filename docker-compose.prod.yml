x-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "50m"
    max-file: "6"

x-base: &base
  build:
    context: .
    dockerfile: deployment/FrankenPHP.Dockerfile
    cache_from:
      - "laravel/app"
  image: "laravel/app"
  user: "${HOST_UID:-1000}:${HOST_GID:-1000}"
  depends_on:
    redis:
      condition: service_healthy
  networks:
    - net
  restart: always
  security_opt:
    - no-new-privileges:true
  ulimits:
    nofile:
      soft: 20000
      hard: 40000
  logging: *default-logging

services:
  filebeat:
    image: elastic/filebeat:8.10.2
    container_name: filebeat
    command: filebeat -e --strict.perms=false
    user: root
    environment:
      - ELASTICSEARCH_HOSTS=${ES_HOSTS}
      - ELASTICSEARCH_USERNAME=${ES_USERNAME}
      - ELASTICSEARCH_PASSWORD=${ES_PASSWORD}
      - ELASTICSEARCH_SSL_VERIFICATION_MODE=none
    depends_on:
      php:
        condition: service_healthy
      horizon:
        condition: service_healthy
      scheduler:
        condition: service_started
      redis:
        condition: service_healthy
    networks:
      - net
    restart: unless-stopped
    logging: *default-logging
    volumes:
      - ./deployment/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro

  horizon:
    <<: *base
    environment:
      CONTAINER_MODE: horizon
    labels:
      traefik.enable: false
    healthcheck:
      test: [ "CMD", "frankenphp", "php-cli", "artisan", "horizon:status" ]
      timeout: 10s
      retries: 3

  kibana:
    image: elastic/kibana:8.10.2
    container_name: kibana
    environment:
      - ELASTICSEARCH_HOSTS=["http://***********:9200"]
      - ELASTICSEARCH_SERVICEACCOUNTTOKEN=${KIBANA_SERVICE_TOKEN}
      - SERVER_HOST=0.0.0.0
      - SERVER_PUBLICBASEURL=https://k.gunpost.tech
    networks:
      - net
      - traefik
    volumes:
      - kibana-data:/usr/share/kibana/data
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik"
      - "traefik.http.routers.kibana.rule=Host(`k.gunpost.tech`)"
      - "traefik.http.routers.kibana.entrypoints=websecure"
      - "traefik.http.routers.kibana.tls.certresolver=myresolver"
      - "traefik.http.routers.kibana.service=kibana"
      - "traefik.http.services.kibana.loadbalancer.server.port=5601"
    logging:
      driver: "none"

  php:
    <<: *base
    networks:
      - net
      - traefik
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik"
      - "traefik.http.routers.gunpost-php.rule=Host(`api.gunpost.ru`)"
      - "traefik.http.routers.gunpost-php.entrypoints=websecure"
      - "traefik.http.routers.gunpost-php.tls.certresolver=myresolver"
      - "traefik.http.routers.gunpost-php.service=gunpost-php"
      - "traefik.http.services.gunpost-php.loadbalancer.server.port=8000"
    healthcheck:
      test: [ "CMD", "curl", "--fail", "localhost:8000/up" ]
      interval: 3s
      timeout: 5s
      retries: 12
    logging:
      driver: "json-file"
      options:
        env: "production"
        labels: "logging"
        max-file: "3"
        max-size: "10m"
        tag: "gunpost-php/{{.ID}}"

  redis:
    image: "redis:alpine"
    command: [ "redis-server", "--maxmemory", "2gb", "--maxmemory-policy", "allkeys-lru" ]
    networks:
      - net
    restart: always
    labels:
      traefik.enable: false
    security_opt:
      - no-new-privileges:true
    ulimits:
      nofile:
        soft: 20000
        hard: 40000
    logging: *default-logging
    volumes:
      - "redis:/data"
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      timeout: 5s
      retries: 3

  reverb:
    <<: *base
    environment:
      CONTAINER_MODE: reverb
    networks:
      - net
      - traefik
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik"
      - "traefik.http.routers.gunpost-reverb.rule=Host(`ws.gunpost.ru`)"
      - "traefik.http.routers.gunpost-reverb.entrypoints=websecure"
      - "traefik.http.routers.gunpost-reverb.tls.certresolver=myresolver"
      - "traefik.http.routers.gunpost-reverb.service=gunpost-reverb"
      - "traefik.http.services.gunpost-reverb.loadbalancer.server.port=8080"
    logging:
      driver: "json-file"
      options:
        env: "production"
        labels: "logging"
        max-file: "3"
        max-size: "10m"
        tag: "gunpost-reverb/{{.ID}}"

  scheduler:
    <<: *base
    environment:
      CONTAINER_MODE: scheduler
    labels:
      traefik.enable: false
    logging:
      driver: "json-file"
      options:
        env: "production"
        labels: "logging"
        max-file: "3"
        max-size: "10m"
        tag: "gunpost-scheduler/{{.ID}}"

  tor-proxy:
    image: "dperson/torproxy"
    container_name: gunpost-tor-proxy
    environment:
      - TOR_MaxCircuitDirtiness=1
      - TOR_NewCircuitPeriod=1
      - TOR_EnforceDistinctSubnets=1
    networks:
      - net
    restart: always
    logging:
      driver: "none"

  watermark-api:
    build:
      context: erase-watermark
      dockerfile: Dockerfile
    container_name: gunpost-watermark-api
    depends_on:
      - tor-proxy
    networks:
      - net
    restart: always
    logging:
      driver: "none"

  worker:
    <<: *base
    environment:
      CONTAINER_MODE: worker
      WORKER_COMMAND: php artisan queue:work
    labels:
      traefik.enable: false
    logging:
      driver: "json-file"
      options:
        env: "production"
        labels: "logging"
        max-file: "3"
        max-size: "10m"
        tag: "gunpost-worker/{{.ID}}"

networks:
  net:
    driver: bridge
  traefik:
    external: true

volumes:
  kibana-data:
  redis:
