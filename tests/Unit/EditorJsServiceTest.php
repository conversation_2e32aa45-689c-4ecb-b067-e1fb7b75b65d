<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\EditorJsService;

class EditorJsServiceTest extends TestCase
{
    public function test_parse_empty_html()
    {
        $result = EditorJsService::parseHtml('');
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('blocks', $result);
        $this->assertEmpty($result['blocks']);
    }

    public function test_parse_paragraph()
    {
        $html = '<p>Тестовый параграф</p>';
        $result = EditorJsService::parseHtml($html);
        
        $this->assertCount(1, $result['blocks']);
        $this->assertEquals('paragraph', $result['blocks'][0]['type']);
        $this->assertEquals('Тестовый параграф', $result['blocks'][0]['data']['text']);
    }

    public function test_parse_header()
    {
        $html = '<h2>Заголовок второго уровня</h2>';
        $result = EditorJsService::parseHtml($html);
        
        $this->assertCount(1, $result['blocks']);
        $this->assertEquals('header', $result['blocks'][0]['type']);
        $this->assertEquals('Заголовок второго уровня', $result['blocks'][0]['data']['text']);
        $this->assertEquals(2, $result['blocks'][0]['data']['level']);
    }

    public function test_parse_list()
    {
        $html = '<ul><li>Первый</li><li>Второй</li></ul>';
        $result = EditorJsService::parseHtml($html);
        
        $this->assertCount(1, $result['blocks']);
        $this->assertEquals('list', $result['blocks'][0]['type']);
        $this->assertEquals('unordered', $result['blocks'][0]['data']['style']);
        $this->assertEquals(['Первый', 'Второй'], $result['blocks'][0]['data']['items']);
    }

    public function test_parse_image()
    {
        $html = '<img src="test.jpg" alt="Тест">';
        $result = EditorJsService::parseHtml($html);
        
        $this->assertCount(1, $result['blocks']);
        $this->assertEquals('image', $result['blocks'][0]['type']);
        $this->assertEquals('test.jpg', $result['blocks'][0]['data']['file']['url']);
        $this->assertEquals('Тест', $result['blocks'][0]['data']['caption']);
    }

    public function test_parse_quote()
    {
        $html = '<blockquote>Цитата</blockquote>';
        $result = EditorJsService::parseHtml($html);
        
        $this->assertCount(1, $result['blocks']);
        $this->assertEquals('quote', $result['blocks'][0]['type']);
        $this->assertEquals('Цитата', $result['blocks'][0]['data']['text']);
    }

    public function test_parse_code()
    {
        $html = '<pre><code>console.log("test");</code></pre>';
        $result = EditorJsService::parseHtml($html);
        
        $this->assertCount(1, $result['blocks']);
        $this->assertEquals('code', $result['blocks'][0]['type']);
        $this->assertEquals('console.log("test");', $result['blocks'][0]['data']['code']);
    }

    public function test_parse_delimiter()
    {
        $html = '<hr>';
        $result = EditorJsService::parseHtml($html);
        
        $this->assertCount(1, $result['blocks']);
        $this->assertEquals('delimiter', $result['blocks'][0]['type']);
        $this->assertEmpty($result['blocks'][0]['data']);
    }

    public function test_parse_complex_html()
    {
        $html = '
            <h1>Заголовок</h1>
            <p>Параграф с <b>жирным</b> текстом</p>
            <ul>
                <li>Элемент 1</li>
                <li>Элемент 2</li>
            </ul>
        ';
        
        $result = EditorJsService::parseHtml($html);
        
        $this->assertCount(3, $result['blocks']);
        $this->assertEquals('header', $result['blocks'][0]['type']);
        $this->assertEquals('paragraph', $result['blocks'][1]['type']);
        $this->assertEquals('list', $result['blocks'][2]['type']);
    }
}
