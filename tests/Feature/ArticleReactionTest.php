<?php

namespace Tests\Feature;

use App\Models\Article;
use App\Models\User;
use App\Models\ArticleCategory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ArticleReactionTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Создаем типы реакций
        $this->artisan('love:reaction-type-add', ['name' => 'like']);
        $this->artisan('love:reaction-type-add', ['name' => 'love']);
        $this->artisan('love:reaction-type-add', ['name' => 'fire']);
        $this->artisan('love:reaction-type-add', ['name' => 'wow']);
        $this->artisan('love:reaction-type-add', ['name' => 'sad']);
        $this->artisan('love:reaction-type-add', ['name' => 'angry']);
    }

    public function test_user_can_react_to_article()
    {
        $user = User::factory()->create();
        $category = ArticleCategory::factory()->create();
        $article = Article::factory()->create([
            'category_id' => $category->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        $response = $this->actingAs($user)
            ->postJson("/api/articles/{$article->id}/react", [
                'type' => 'like'
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'action' => 'added',
                'success' => true
            ]);

        // Проверяем, что реакция была добавлена
        $reacterFacade = $user->viaLoveReacter();
        $this->assertTrue($reacterFacade->hasReactedTo($article, 'like'));
    }

    public function test_user_can_remove_reaction_by_reacting_again()
    {
        $user = User::factory()->create();
        $category = ArticleCategory::factory()->create();
        $article = Article::factory()->create([
            'category_id' => $category->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        // Добавляем реакцию
        $reacterFacade = $user->viaLoveReacter();
        $reacterFacade->reactTo($article, 'like');

        // Удаляем реакцию, отправив ту же реакцию
        $response = $this->actingAs($user)
            ->postJson("/api/articles/{$article->id}/react", [
                'type' => 'like'
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'action' => 'removed',
                'success' => true
            ]);

        // Проверяем, что реакция была удалена
        $this->assertFalse($reacterFacade->hasReactedTo($article, 'like'));
    }

    public function test_user_can_change_reaction_type()
    {
        $user = User::factory()->create();
        $category = ArticleCategory::factory()->create();
        $article = Article::factory()->create([
            'category_id' => $category->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        $reacterFacade = $user->viaLoveReacter();
        
        // Добавляем первую реакцию
        $reacterFacade->reactTo($article, 'like');

        // Меняем на другую реакцию
        $response = $this->actingAs($user)
            ->postJson("/api/articles/{$article->id}/react", [
                'type' => 'love'
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'action' => 'added',
                'success' => true
            ]);

        // Проверяем, что старая реакция удалена, а новая добавлена
        $this->assertFalse($reacterFacade->hasReactedTo($article, 'like'));
        $this->assertTrue($reacterFacade->hasReactedTo($article, 'love'));
    }

    public function test_article_show_returns_reaction_counters()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $category = ArticleCategory::factory()->create();
        $article = Article::factory()->create([
            'category_id' => $category->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        // Добавляем реакции от разных пользователей
        $user1->viaLoveReacter()->reactTo($article, 'like');
        $user2->viaLoveReacter()->reactTo($article, 'love');

        $response = $this->actingAs($user1)
            ->getJson("/api/articles/{$article->id}");

        $response->assertStatus(200);
        
        $data = $response->json();
        $this->assertArrayHasKey('reactions', $data);
        $this->assertArrayHasKey('counters', $data['reactions']);
        $this->assertEquals(1, $data['reactions']['counters']['like'] ?? 0);
        $this->assertEquals(1, $data['reactions']['counters']['love'] ?? 0);
        $this->assertEquals('like', $data['reactions']['user_reaction']);
    }

    public function test_user_can_unreact_from_article()
    {
        $user = User::factory()->create();
        $category = ArticleCategory::factory()->create();
        $article = Article::factory()->create([
            'category_id' => $category->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        // Добавляем реакцию
        $reacterFacade = $user->viaLoveReacter();
        $reacterFacade->reactTo($article, 'like');

        // Удаляем все реакции
        $response = $this->actingAs($user)
            ->deleteJson("/api/articles/{$article->id}/unreact");

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        // Проверяем, что реакция была удалена
        $this->assertFalse($reacterFacade->hasReactedTo($article, 'like'));
    }
}
