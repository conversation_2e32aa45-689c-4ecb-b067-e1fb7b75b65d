filebeat.inputs:
  - type: filestream
    id: docker
    paths:
      - /var/lib/docker/containers/*/*.log
    parsers:
      - container:
          stream: all
    # Отсечём заведомо шумные строки ещё на уровне строки
    exclude_lines:
      - '"GET /health"'
      - '"GET /metrics"'
      - '"OPTIONS '
      - 'level=info'
    processors:
      - add_docker_metadata: ~
      - decode_json_fields:
          when:
            regexp:
              message: '^\s*\{.*\}\s*$'
          fields: ["message"]
          target: ""
          overwrite_keys: true
          add_error_key: true
      # Оставляем ТОЛЬКО ошибки/критичные записи Monolog/Laravel
      - drop_event:
          when:
            not:
              or:
                - equals:
                    level: error
                - equals:
                    level: critical
                - equals:
                    level: alert
                - equals:
                    level: emergency
                - equals:
                    level_name: ERROR
                - equals:
                    level_name: CRITICAL
                - equals:
                    level_name: ALERT
                - equals:
                    level_name: EMERGENCY
                - equals:
                    log.level: error
                - equals:
                    log.level: critical
                - equals:
                    severity: error
                - regexp:
                    message: '(?i)\\b(error|critical|alert|emergency|exception|throwable)\\b'
                - contains:
                    log.level: error
      # Отсечь типовой мусор: 404 статике/фавиконки, отвалившиеся клиенты и т.п.
      - drop_event:
          when:
            or:
              - equals:
                  status: 404
              - regexp:
                  message: '^(GET|HEAD) /(favicon\\.ico|robots\\.txt|assets|static|_nuxt|js|css|images)/'
              - contains:
                  message: "ClientAbortException"
      - drop_event:
          when:
            and:
              - regexp:
                  message: '^time=\".*\" level=(info|debug|notice) '
              - not:
                  regexp:
                    message: '(?i)\\b(error|exception|failed|failure)\\b'
processors:
  - add_docker_metadata: ~
  - add_host_metadata: ~
  - add_fields:
      target: ""
      fields:
        env: production
        project: gunpost
  - drop_fields:
      fields:
        - agent
        - ecs
        - input
        - log.file
        - host
        - docker.attrs
        - container.labels
        - container.image
        - stream
        - error.data
        - error.field
        - error.type
      ignore_missing: true
output.elasticsearch:
  hosts: ${ELASTICSEARCH_HOSTS}
  username: ${ELASTICSEARCH_USERNAME}
  password: ${ELASTICSEARCH_PASSWORD}
  ssl:
    verification_mode: none
  index: "gunpost-logs"
# Отключаем ILM и загрузку шаблонов, чтобы Filebeat не создавал data stream
setup.ilm.enabled: false
setup.template.enabled: false
logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644
