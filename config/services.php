<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'dadata' => [
        'api_key' => env('DADATA_API_KEY'),
        'api_secret' => env('DADATA_API_SECRET'),
    ],

    'proxy' => [
        'url' => env('PROXY_URL'),
    ],

    'robokassa' => [
        'login' => env('ROBOKASSA_LOGIN'),
        'password1' => env('ROBOKASSA_PASSWORD1'),
        'password2' => env('ROBOKASSA_PASSWORD2'),
        'is_test' => env('ROBOKASSA_IS_TEST', false),
        'test_password1' => env('ROBOKASSA_TEST_PASSWORD1'),
        'test_password2' => env('ROBOKASSA_TEST_PASSWORD2'),
        'hashType' => env('ROBOKASSA_HASH_TYPE', 'md5'),
    ],

    'zvonok' => [
        'public_key' => env('ZVONOK_PUBLIC_KEY'),
        'campaign_id' => env('ZVONOK_CAMPAIGN_ID'),
    ],

    'text_ru' => [
        'userkey' => env('TEXT_RU_USERKEY'),
    ],

    'telegram-bot-api' => [
        'bot_name' => env('TELEGRAM_BOT_NAME', 'YOUR BOT NAME HERE'),
        'token' => env('TELEGRAM_BOT_TOKEN', 'YOUR BOT TOKEN HERE'),
        'service_chat_id' => env('TELEGRAM_BOT_SERVICE_CHAT_ID'),
    ],

    'yookassa' => [
        'shop_id' => env('YOOKASSA_SHOP_ID'),
        'password' => env('YOOKASSA_PASSWORD'),
    ],

    'static_api_key' => env('STATIC_API_KEY'),

];
