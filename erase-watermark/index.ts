import axios from "axios";
import { SignJWT } from "jose";

export default class DeWatermark {
    private agent?: any;

    constructor(agent?: any) {
        this.agent = agent;
    }

    public async eraseWatermark(image: Buffer) {
        try {
            console.log("Получение IP адреса...");
            const { data: ip } = await axios.get("https://icanhazip.com", {
                httpsAgent: this.agent,
            });
            console.log(`IP адрес: ${ip.trim()}`);

            console.log("Загрузка HTML страницы...");
            const { data: html } = await axios.get("https://dewatermark.ai/upload", {
                httpsAgent: this.agent,
            });

            console.log("Извлечение URL JS файла...");
            const jsUrlPart = html.split("/_next/static/chunks/pages/_app")[1]?.split(".js")[0];
            if (!jsUrlPart) {
                throw new Error("Не удалось найти JS файл на странице");
            }

            const jsUrl = `https://dewatermark.ai/_next/static/chunks/pages/_app${jsUrlPart}.js`;
            console.log(`JS URL: ${jsUrl}`);

            console.log("Загрузка JS файла...");
            const { data: js } = await axios.get(jsUrl, {
                httpsAgent: this.agent,
            });

            console.log(`Размер JS файла: ${js.length} символов`);

            // Сохраним фрагмент JS файла для анализа
            console.log("=== Фрагмент JS файла (первые 1000 символов) ===");
            console.log(js.substring(0, 1000));
            console.log("=== Конец фрагмента ===");

            console.log("Извлечение JWT ключа...");
            const jwtKey = this.extractJwtKey(js);
            console.log(`JWT ключ: ${jwtKey}`);
            console.log(`Длина ключа: ${jwtKey.length}`);

            if (!this.isValidBase64(jwtKey)) {
                throw new Error(`Извлеченный JWT ключ не является валидным base64: ${jwtKey}`);
            }

            console.log("Создание API ключа...");
            const apiKey = "Bearer " + await this.createJWT(this.base64ToUint8Array(jwtKey), false);

            console.log("Подготовка payload для API запроса...");
            const payload = new FormData();
            payload.append("original_preview_image", new Blob([image]), "image.png");
            payload.append("zoom_factor", "2");

            console.log("Отправка запроса к API dewatermark.ai...");
            const { data } = await axios.post("https://api.dewatermark.ai/api/object_removal/v5/erase_watermark", payload, {
                headers: {
                    "X-Api-Mode": "AUTO",
                    "X-Service": "REMOVE_WATERMARK",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    "Referer": "https://dewatermark.ai/",
                    "Origin": "https://dewatermark.ai",
                    "Host": "api.dewatermark.ai",
                    "Authorization": apiKey
                },
                httpsAgent: this.agent
            });

            if (!data.edited_image) {
                console.error("API ответ без edited_image:", data);
                throw new Error(`API не вернул обработанное изображение: ${JSON.stringify(data)}`);
            }

            console.log("Изображение успешно обработано");
            return Buffer.from(data.edited_image.image, "base64");

        } catch (error) {
            console.error("Ошибка в eraseWatermark:", error);
            throw error;
        }
    }

    private extractJwtKey(jsContent: string): string {
        console.log("Пробуем разные методы извлечения JWT ключа...");

        // Ищем все строки, содержащие api.dewatermark.ai
        const apiMatches = jsContent.match(/[^"]*api\.dewatermark\.ai[^"]*/g);
        console.log("Найденные фрагменты с api.dewatermark.ai:", apiMatches);

        // Ищем все base64-подобные строки длиной 40+
        const base64Matches = jsContent.match(/[A-Za-z0-9+/]{40,}={0,2}/g);
        console.log("Найденные base64-подобные строки:", base64Matches?.slice(0, 5)); // показываем первые 5

        // Попробуем найти секретный ключ для JWT
        const jwtKeyPatterns = [
            // Исправленные регулярные выражения
            /["'](https:\/\/api\.dewatermark\.ai)["']\s*[,:}]\s*["']([A-Za-z0-9+/]{40,}={0,2})["']/,

            // Поиск ключа перед или после API URL
            /["']([A-Za-z0-9+/]{40,}={0,2})["'][^"]{0,100}api\.dewatermark\.ai/,
            /api\.dewatermark\.ai[^"]{0,100}["']([A-Za-z0-9+/]{40,}={0,2})["']/,

            // Поиск в объектах конфигурации
            /["'](secret|key|token|auth)["']\s*:\s*["']([A-Za-z0-9+/]{40,}={0,2})["']/i,

            // Общий поиск валидных base64 строк
            /["']([A-Za-z0-9+/]{44}={0,2})["']/,
            /["']([A-Za-z0-9+/]{64}={0,2})["']/
        ];

        console.log("Применяем паттерны для поиска JWT ключа...");

        for (let i = 0; i < jwtKeyPatterns.length; i++) {
            const matches = jsContent.match(jwtKeyPatterns[i]);
            if (matches) {
                console.log(`Паттерн ${i + 1} нашел совпадения:`, matches);
                const potentialKey = matches[1] || matches[2];
                if (potentialKey && potentialKey !== "https://api.dewatermark.ai" && this.isValidBase64(potentialKey)) {
                    console.log(`Паттерн ${i + 1} дал валидный ключ: ${potentialKey}`);
                    return potentialKey;
                }
            }
        }

        // Если ничего не найдено, попробуем оригинальный метод с отладкой
        console.log("Пробуем оригинальный метод...");
        const parts = jsContent.split("https://api.dewatermark.ai\"");
        console.log(`Разделено на ${parts.length} частей`);

        if (parts.length > 1) {
            console.log("Фрагмент после API URL (первые 200 символов):");
            console.log(parts[1].substring(0, 200));

            const afterApi = parts[1];
            const quoteParts = afterApi.split("\"");
            console.log("Части после разделения по кавычкам (первые 10):");
            for (let i = 0; i < Math.min(quoteParts.length, 10); i++) {
                console.log(`  [${i}]: "${quoteParts[i]}"`);
            }

            for (let i = 1; i < Math.min(quoteParts.length, 10); i++) {
                const candidate = quoteParts[i];
                if (candidate && candidate.length > 20 && this.isValidBase64(candidate)) {
                    console.log(`Найден валидный base64 ключ: ${candidate}`);
                    return candidate;
                }
            }
        }

        // Последний шанс - ищем все возможные base64 строки и проверяем их
        console.log("Последняя попытка - проверяем все найденные base64 строки...");
        if (base64Matches) {
            for (const match of base64Matches) {
                if (match.length >= 32 && this.isValidBase64(match)) {
                    console.log(`Найден потенциальный ключ: ${match}`);
                    return match;
                }
            }
        }

        throw new Error("Не удалось извлечь JWT ключ из JS файла");
    }

    private isValidBase64(str: string): boolean {
        if (!str || str.length < 20) return false;

        // Проверяем базовый формат base64
        const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
        if (!base64Regex.test(str)) return false;

        try {
            const decoded = atob(str);
            return btoa(decoded) === str;
        } catch (err) {
            return false;
        }
    }

    private base64ToUint8Array(base64: string): Uint8Array {
        try {
            if (!this.isValidBase64(base64)) {
                throw new Error(`Неверная base64 строка: ${base64}`);
            }

            const binaryString = atob(base64);
            const len = binaryString.length;
            const bytes = new Uint8Array(len);
            for (let i = 0; i < len; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }
            return bytes;
        } catch (error) {
            console.error("Ошибка конвертации base64 в Uint8Array:", error);
            throw new Error(`Не удалось декодировать base64: ${error.message}`);
        }
    }

    private async createJWT(keyString: Uint8Array, er: boolean): Promise<string> {
        try {
            const key = await this.importKey(keyString);

            const jwt = await new SignJWT({
                sub: "ignore",
                platform: "web",
                is_pro: er,
                exp: Math.round(Date.now() / 1000) + 300
            })
                .setProtectedHeader({
                    alg: "HS256",
                    typ: "JWT"
                })
                .sign(key);

            return jwt;
        } catch (error) {
            console.error("Ошибка создания JWT:", error);
            throw error;
        }
    }

    private async importKey(en: Uint8Array): Promise<CryptoKey> {
        try {
            return await crypto.subtle.importKey(
                "raw",
                en,
                { name: "HMAC", hash: "SHA-256" },
                false,
                ["sign", "verify"]
            );
        } catch (error) {
            console.error("Ошибка импорта ключа:", error);
            throw error;
        }
    }
}
