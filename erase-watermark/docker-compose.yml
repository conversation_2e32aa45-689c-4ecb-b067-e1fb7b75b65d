services:
    # Сервис с прокси через Tor
    tor-proxy:
        image: "dperson/torproxy"
        container_name: tor-proxy
        environment:
            - TOR_MaxCircuitDirtiness=1
            - TOR_NewCircuitPeriod=1
            - TOR_EnforceDistinctSubnets=1
        ports:
            - "9050:9050"
        networks:
            - watermark-network
        restart: unless-stopped
        healthcheck:
            test: ["CMD", "curl", "-f", "--socks5-hostname", "localhost:9050", "http://httpbin.org/ip"]
            interval: 30s
            timeout: 10s
            retries: 3
            start_period: 10s

    # Ваш API-сервис
    watermark-api:
        build: .
        container_name: watermark-api
        ports:
            - "3002:3001"
        environment:
            - NODE_ENV=production
            - PORT=3001
        # Убрал volumes для production, но можно оставить для разработки
        # volumes:
        #   - .:/app  # Только для разработки
        networks:
            - watermark-network
        depends_on:
            tor-proxy:
                condition: service_healthy
        restart: unless-stopped
        healthcheck:
            test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3001/health"]
            interval: 30s
            timeout: 10s
            retries: 3
            start_period: 10s

networks:
    watermark-network:
        driver: bridge
        name: watermark-network
