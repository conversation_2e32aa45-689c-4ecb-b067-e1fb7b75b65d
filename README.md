### Миграции
```bash
php artisan migrate --path=/database/migrations/elasticsearch
```

### Install local vendor

```
docker run --rm \
    -u "$(id -u):$(id -g)" \
    -v "$(pwd):/var/www/html" \
    -w /var/www/html \
    laravelsail/php84-composer:latest \
    composer install --ignore-platform-reqs --no-dev
```

### Загрузить дамп в локальную БД
``
cat gunpost.sql | docker exec -i apigunpostru-pgsql-1 \
  psql -U sail -d laravel
``
