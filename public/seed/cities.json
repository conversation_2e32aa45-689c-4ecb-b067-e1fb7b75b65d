[{"id": 3, "order": 2, "name": "Санкт-Петербург", "name_pred": "Санкт-Петербурге", "slug": "sankt-peterburg", "region_number": 78, "postal_code": 190000, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-SPE", "region_type": "г", "region_type_full": "город", "region": "Санкт-Петербург", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": 59.9390012, "lng": 30.3158184, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:47.000000Z"}, {"id": 4, "order": 3, "name": "Республика Адыгея", "name_pred": "Республике Адыгея", "slug": "respublika-adygeia", "region_number": 1, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-AD", "region_type": "Респ", "region_type_full": "республика", "region": "Адыгея", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:47.000000Z"}, {"id": 6, "order": 5, "name": "Республика Бурятия", "name_pred": "Республике Бурятия", "slug": "respublika-buriatiia", "region_number": 3, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-BU", "region_type": "Респ", "region_type_full": "республика", "region": "Бурятия", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:47.000000Z"}, {"id": 7, "order": 6, "name": "Республика Алтай", "name_pred": "Республике Алтай", "slug": "respublika-altai", "region_number": 4, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-AL", "region_type": "Респ", "region_type_full": "республика", "region": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:47.000000Z"}, {"id": 9, "order": 8, "name": "Республика Ингушетия", "name_pred": "Республике Ингушетия", "slug": "respublika-ingusetiia", "region_number": 6, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-IN", "region_type": "Респ", "region_type_full": "республика", "region": "Ингушетия", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:47.000000Z"}, {"id": 11, "order": 10, "name": "Республика Калмыкия", "name_pred": "Республике Калмыкия", "slug": "respublika-kalmykiia", "region_number": 8, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-KL", "region_type": "Респ", "region_type_full": "республика", "region": "Калмыкия", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:48.000000Z"}, {"id": 12, "order": 11, "name": "Карачаево-Черкесская Республика", "name_pred": "Карачаево-Черкесской Республике", "slug": "karacaevo-cerkesskaia-respublika", "region_number": 9, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-KC", "region_type": "Респ", "region_type_full": "республика", "region": "Карачаево-Черкесская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:48.000000Z"}, {"id": 14, "order": 13, "name": "Республика Коми", "name_pred": "Республике Коми", "slug": "respublika-komi", "region_number": 11, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-KO", "region_type": "Респ", "region_type_full": "республика", "region": "Коми", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:48.000000Z"}, {"id": 16, "order": 15, "name": "Республика Мордовия", "name_pred": "Республике Мордовия", "slug": "respublika-mordoviia", "region_number": 13, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-MO", "region_type": "Респ", "region_type_full": "республика", "region": "Мордовия", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:49.000000Z"}, {"id": 18, "order": 17, "name": "Республика Северная Осетия - Алания", "name_pred": "Республике Северная Осетия - Алания", "slug": "respublika-severnaia-osetiia-alaniia", "region_number": 15, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-SE", "region_type": "Респ", "region_type_full": "республика", "region": "Северная Осетия - Алания", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:50.000000Z"}, {"id": 19, "order": 18, "name": "Республика Татарстан", "name_pred": "Республике Татарстан", "slug": "respublika-tatarstan", "region_number": 16, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-TA", "region_type": "Респ", "region_type_full": "республика", "region": "Тата<PERSON><PERSON>тан", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:50.000000Z"}, {"id": 21, "order": 20, "name": "Удмуртская Республика", "name_pred": "Удмуртской Республике", "slug": "udmurtskaia-respublika", "region_number": 18, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-UD", "region_type": "Респ", "region_type_full": "республика", "region": "Удмуртская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:52.000000Z"}, {"id": 22, "order": 21, "name": "Республика Хакасия", "name_pred": "Республике Хакасия", "slug": "respublika-xakasiia", "region_number": 19, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-KK", "region_type": "Респ", "region_type_full": "республика", "region": "Хакасия", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:52.000000Z"}, {"id": 24, "order": 23, "name": "Алтайский край", "name_pred": "Алтайском крае", "slug": "altaiskii-krai", "region_number": 22, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-ALT", "region_type": "край", "region_type_full": "край", "region": "Алтайский", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:55.000000Z"}, {"id": 26, "order": 25, "name": "Красноярский край", "name_pred": "Красноярском крае", "slug": "krasnoiarskii-krai", "region_number": 24, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-KYA", "region_type": "край", "region_type_full": "край", "region": "Красноярский", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:55.000000Z"}, {"id": 28, "order": 27, "name": "Ставропольский край", "name_pred": "Ставропольском крае", "slug": "stavropolskii-krai", "region_number": 26, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-STA", "region_type": "край", "region_type_full": "край", "region": "Ставропольский", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:55.000000Z"}, {"id": 30, "order": 29, "name": "Амурская область", "name_pred": "Амурской области", "slug": "amurskaia-oblast", "region_number": 28, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-AMU", "region_type": "обл", "region_type_full": "область", "region": "Амурская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:56.000000Z"}, {"id": 31, "order": 30, "name": "Архангельская область", "name_pred": "Архангельской области", "slug": "arxangelskaia-oblast", "region_number": 29, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-ARK", "region_type": "обл", "region_type_full": "область", "region": "Архангельская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:56.000000Z"}, {"id": 33, "order": 32, "name": "Белгородская область", "name_pred": "Белгородской области", "slug": "belgorodskaia-oblast", "region_number": 31, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-BEL", "region_type": "обл", "region_type_full": "область", "region": "Белгородская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:57.000000Z"}, {"id": 34, "order": 33, "name": "Брянская область", "name_pred": "Брянской области", "slug": "brianskaia-oblast", "region_number": 32, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-BRY", "region_type": "обл", "region_type_full": "область", "region": "Брянская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:57.000000Z"}, {"id": 36, "order": 35, "name": "Волгоградская область", "name_pred": "Волгоградской области", "slug": "volgogradskaia-oblast", "region_number": 34, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-VGG", "region_type": "обл", "region_type_full": "область", "region": "Волгоградская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:57.000000Z"}, {"id": 37, "order": 36, "name": "Вологодская область", "name_pred": "Вологодской области", "slug": "vologodskaia-oblast", "region_number": 35, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-VLG", "region_type": "обл", "region_type_full": "область", "region": "Вологодская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:58.000000Z"}, {"id": 39, "order": 38, "name": "Ивановская область", "name_pred": "Ивановской области", "slug": "ivanovskaia-oblast", "region_number": 37, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-IVA", "region_type": "обл", "region_type_full": "область", "region": "Ивановская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:58.000000Z"}, {"id": 40, "order": 39, "name": "Иркутская область", "name_pred": "Иркутской области", "slug": "irkutskaia-oblast", "region_number": 38, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-IRK", "region_type": "обл", "region_type_full": "область", "region": "Иркутская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:59.000000Z"}, {"id": 42, "order": 41, "name": "Калужская область", "name_pred": "Калужской области", "slug": "kaluzskaia-oblast", "region_number": 40, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-KLU", "region_type": "обл", "region_type_full": "область", "region": "Калужская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:59.000000Z"}, {"id": 43, "order": 42, "name": "Камчатский край", "name_pred": "Камчатском крае", "slug": "kamcatskii-krai", "region_number": 41, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-KAM", "region_type": "край", "region_type_full": "край", "region": "Камчатский", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:00.000000Z"}, {"id": 45, "order": 44, "name": "Кировская область", "name_pred": "Кировской области", "slug": "kirovskaia-oblast", "region_number": 43, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-KIR", "region_type": "обл", "region_type_full": "область", "region": "Кировская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:00.000000Z"}, {"id": 46, "order": 45, "name": "Костромская область", "name_pred": "Костромской области", "slug": "kostromskaia-oblast", "region_number": 44, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-KOS", "region_type": "обл", "region_type_full": "область", "region": "Костромская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:00.000000Z"}, {"id": 48, "order": 47, "name": "Курская область", "name_pred": "Курской области", "slug": "kurskaia-oblast", "region_number": 46, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-KRS", "region_type": "обл", "region_type_full": "область", "region": "Курская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:00.000000Z"}, {"id": 50, "order": 49, "name": "Липецкая область", "name_pred": "Липецкой области", "slug": "lipeckaia-oblast", "region_number": 48, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-LIP", "region_type": "обл", "region_type_full": "область", "region": "Липецкая", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:01.000000Z"}, {"id": 52, "order": 51, "name": "Мурманская область", "name_pred": "Мурманской области", "slug": "murmanskaia-oblast", "region_number": 51, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-MUR", "region_type": "обл", "region_type_full": "область", "region": "Мурманская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:01.000000Z"}, {"id": 54, "order": 53, "name": "Новгородская область", "name_pred": "Новгородской области", "slug": "novgorodskaia-oblast", "region_number": 53, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-NGR", "region_type": "обл", "region_type_full": "область", "region": "Новгородская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:01.000000Z"}, {"id": 55, "order": 54, "name": "Новосибирская область", "name_pred": "Новосибирской области", "slug": "novosibirskaia-oblast", "region_number": 54, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-NVS", "region_type": "обл", "region_type_full": "область", "region": "Новосибирская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:01.000000Z"}, {"id": 58, "order": 57, "name": "Орловская область", "name_pred": "Орловской области", "slug": "orlovskaia-oblast", "region_number": 57, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-ORL", "region_type": "обл", "region_type_full": "область", "region": "Орловская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:01.000000Z"}, {"id": 59, "order": 58, "name": "Пензенская область", "name_pred": "Пензенской области", "slug": "penzenskaia-oblast", "region_number": 58, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-PNZ", "region_type": "обл", "region_type_full": "область", "region": "Пензенская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:01.000000Z"}, {"id": 25, "order": 24, "name": "Краснодарский край", "name_pred": "Краснодарском крае", "slug": "krasnodarskii-krai", "region_number": 23, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-KDA", "region_type": "край", "region_type_full": "край", "region": "Краснодарский", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:24:03.000000Z"}, {"id": 49, "order": 48, "name": "Ленинградская область", "name_pred": "Ленинградской области", "slug": "leningradskaia-oblast", "region_number": 47, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-LEN", "region_type": "обл", "region_type_full": "область", "region": "Ленинградская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:24:03.000000Z"}, {"id": 1, "order": 0, "name": "Москва", "name_pred": "Москве", "slug": "moskva", "region_number": 77, "postal_code": 101000, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-MOW", "region_type": "г", "region_type_full": "город", "region": "Москва", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": 55.7540584, "lng": 37.62049, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:06.000000Z"}, {"id": 2, "order": 1, "name": "Московская область", "name_pred": "Московской области", "slug": "moskovskaia-oblast", "region_number": 50, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-MOS", "region_type": "обл", "region_type_full": "область", "region": "Московская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:47.000000Z"}, {"id": 5, "order": 4, "name": "Республика Башкортостан", "name_pred": "Республике Башкортостан", "slug": "respublika-baskortostan", "region_number": 2, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-BA", "region_type": "Респ", "region_type_full": "республика", "region": "Башкортостан", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:47.000000Z"}, {"id": 8, "order": 7, "name": "Республика Дагестан", "name_pred": "Республике Дагестан", "slug": "respublika-dagestan", "region_number": 5, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-DA", "region_type": "Респ", "region_type_full": "республика", "region": "Даг<PERSON><PERSON>тан", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:47.000000Z"}, {"id": 10, "order": 9, "name": "Кабардино-Балкарская Республика", "name_pred": "Кабардино-Балкарской Республике", "slug": "kabardino-balkarskaia-respublika", "region_number": 7, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-KB", "region_type": "Респ", "region_type_full": "республика", "region": "Кабардино-Балкарская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:48.000000Z"}, {"id": 15, "order": 14, "name": "Республика Ма<PERSON>ий Эл", "name_pred": "Республике Марий Эл", "slug": "respublika-marii-el", "region_number": 12, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-ME", "region_type": "Респ", "region_type_full": "республика", "region": "<PERSON><PERSON><PERSON><PERSON>", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:49.000000Z"}, {"id": 17, "order": 16, "name": "Республика Саха (Якутия)", "name_pred": "Республике Саха (Якутия)", "slug": "respublika-saxa-iakutiia", "region_number": 14, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-SA", "region_type": "Респ", "region_type_full": "республика", "region": "Саха (Якутия)", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:50.000000Z"}, {"id": 20, "order": 19, "name": "Республика Тыва", "name_pred": "Республике Тыва", "slug": "respublika-tyva", "region_number": 17, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-TY", "region_type": "Респ", "region_type_full": "республика", "region": "Тыва", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:51.000000Z"}, {"id": 23, "order": 22, "name": "Чувашская Республика", "name_pred": "Чувашской Республике", "slug": "cuvasskaia-respublika", "region_number": 21, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-CU", "region_type": "Чувашия", "region_type_full": "чувашия", "region": "Чувашская Республика", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:54.000000Z"}, {"id": 27, "order": 26, "name": "Приморский край", "name_pred": "Приморском крае", "slug": "primorskii-krai", "region_number": 25, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-PRI", "region_type": "край", "region_type_full": "край", "region": "Приморский", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:55.000000Z"}, {"id": 29, "order": 28, "name": "Хабаровский край", "name_pred": "Х<PERSON>б<PERSON>ровском крае", "slug": "xabarovskii-krai", "region_number": 27, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-KHA", "region_type": "край", "region_type_full": "край", "region": "Хабаровский", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:56.000000Z"}, {"id": 32, "order": 31, "name": "Астраханская область", "name_pred": "Астраханской области", "slug": "astraxanskaia-oblast", "region_number": 30, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-AST", "region_type": "обл", "region_type_full": "область", "region": "Астраханская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:56.000000Z"}, {"id": 35, "order": 34, "name": "Владимирская область", "name_pred": "Владимирской области", "slug": "vladimirskaia-oblast", "region_number": 33, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-VLA", "region_type": "обл", "region_type_full": "область", "region": "Владимирская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:57.000000Z"}, {"id": 38, "order": 37, "name": "Воронежская область", "name_pred": "Воронежской области", "slug": "voronezskaia-oblast", "region_number": 36, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-VOR", "region_type": "обл", "region_type_full": "область", "region": "Воронежская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:58.000000Z"}, {"id": 41, "order": 40, "name": "Калининградская область", "name_pred": "Калининградской области", "slug": "kaliningradskaia-oblast", "region_number": 39, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-KGD", "region_type": "обл", "region_type_full": "область", "region": "Калининградская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:22:59.000000Z"}, {"id": 44, "order": 43, "name": "Кемеровская область", "name_pred": "Кемеровской области", "slug": "kemerovskaia-oblast", "region_number": 42, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-KEM", "region_type": "обл", "region_type_full": "область", "region": "Кемеровская область - Кузбасс", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:00.000000Z"}, {"id": 47, "order": 46, "name": "Курганская область", "name_pred": "Курганской области", "slug": "kurganskaia-oblast", "region_number": 45, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-KGN", "region_type": "обл", "region_type_full": "область", "region": "Курганская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:00.000000Z"}, {"id": 51, "order": 50, "name": "Магаданская область", "name_pred": "Магаданской области", "slug": "magadanskaia-oblast", "region_number": 49, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-MAG", "region_type": "обл", "region_type_full": "область", "region": "Магаданская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:01.000000Z"}, {"id": 53, "order": 52, "name": "Нижегородская область", "name_pred": "Нижегородской области", "slug": "nizegorodskaia-oblast", "region_number": 52, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-NIZ", "region_type": "обл", "region_type_full": "область", "region": "Нижегородская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:01.000000Z"}, {"id": 57, "order": 56, "name": "Оренбургская область", "name_pred": "Оренбургской области", "slug": "orenburgskaia-oblast", "region_number": 56, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-ORE", "region_type": "обл", "region_type_full": "область", "region": "Оренбургская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:01.000000Z"}, {"id": 60, "order": 59, "name": "Пермский край", "name_pred": "Пермском крае", "slug": "permskii-krai", "region_number": 59, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-PER", "region_type": "край", "region_type_full": "край", "region": "Пермский", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:02.000000Z"}, {"id": 61, "order": 60, "name": "Псковская область", "name_pred": "Псковской области", "slug": "pskovskaia-oblast", "region_number": 60, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-PSK", "region_type": "обл", "region_type_full": "область", "region": "Псковская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:02.000000Z"}, {"id": 62, "order": 61, "name": "Ростовская область", "name_pred": "Ростовской области", "slug": "rostovskaia-oblast", "region_number": 61, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-ROS", "region_type": "обл", "region_type_full": "область", "region": "Ростовская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:02.000000Z"}, {"id": 63, "order": 62, "name": "Рязанская область", "name_pred": "Рязанской области", "slug": "riazanskaia-oblast", "region_number": 62, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-RYA", "region_type": "обл", "region_type_full": "область", "region": "Рязанская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:02.000000Z"}, {"id": 64, "order": 63, "name": "Самарская область", "name_pred": "Самарской области", "slug": "samarskaia-oblast", "region_number": 63, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-SAM", "region_type": "обл", "region_type_full": "область", "region": "Самарская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:02.000000Z"}, {"id": 67, "order": 66, "name": "Свердловская область", "name_pred": "Свердловской области", "slug": "sverdlovskaia-oblast", "region_number": 66, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-SVE", "region_type": "обл", "region_type_full": "область", "region": "Свердловская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:03.000000Z"}, {"id": 68, "order": 67, "name": "Смоленская область", "name_pred": "Смоленской области", "slug": "smolenskaia-oblast", "region_number": 67, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-SMO", "region_type": "обл", "region_type_full": "область", "region": "Смоленская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:03.000000Z"}, {"id": 70, "order": 69, "name": "Тверская область", "name_pred": "Тверской области", "slug": "tverskaia-oblast", "region_number": 69, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-TVE", "region_type": "обл", "region_type_full": "область", "region": "Тверская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:03.000000Z"}, {"id": 71, "order": 70, "name": "Томская область", "name_pred": "Томской области", "slug": "tomskaia-oblast", "region_number": 70, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-TOM", "region_type": "обл", "region_type_full": "область", "region": "Томская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:03.000000Z"}, {"id": 73, "order": 72, "name": "Тюменская область", "name_pred": "Тюменской области", "slug": "tiumenskaia-oblast", "region_number": 72, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-TYU", "region_type": "обл", "region_type_full": "область", "region": "Тюменская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:03.000000Z"}, {"id": 74, "order": 73, "name": "Ульяновская область", "name_pred": "Ульяновской области", "slug": "ulianovskaia-oblast", "region_number": 73, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-ULY", "region_type": "обл", "region_type_full": "область", "region": "Ульяновская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:58.000000Z", "updated_at": "2025-03-12T18:23:03.000000Z"}, {"id": 76, "order": 75, "name": "Забайкальский край", "name_pred": "Забайкальском крае", "slug": "zabaikalskii-krai", "region_number": 75, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-ZAB", "region_type": "край", "region_type_full": "край", "region": "Забайкальский", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:58.000000Z", "updated_at": "2025-03-12T18:23:03.000000Z"}, {"id": 77, "order": 76, "name": "Ярославская область", "name_pred": "Ярославской области", "slug": "iaroslavskaia-oblast", "region_number": 76, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-YAR", "region_type": "обл", "region_type_full": "область", "region": "Ярославская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:58.000000Z", "updated_at": "2025-03-12T18:23:04.000000Z"}, {"id": 79, "order": 78, "name": "Республика Крым", "name_pred": "Республике Крым", "slug": "respublika-krym", "region_number": 82, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "UA-43", "region_type": "Респ", "region_type_full": "республика", "region": "Крым", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:58.000000Z", "updated_at": "2025-03-12T18:23:04.000000Z"}, {"id": 81, "order": 80, "name": "ХМАО - Югра", "name_pred": "ХМАО - Югра", "slug": "xmao-iugra", "region_number": 86, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-KHM", "region_type": "АО", "region_type_full": "автономный округ", "region": "Ханты-Мансийский Автономный округ - Югра", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:58.000000Z", "updated_at": "2025-03-12T18:23:04.000000Z"}, {"id": 83, "order": 82, "name": "город Севастополь", "name_pred": "городе Севастополь", "slug": "gorod-sevastopol", "region_number": 92, "postal_code": 299700, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "UA-40", "region_type": "г", "region_type_full": "город", "region": "Севастополь", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": 44.6167087, "lng": 33.5253617, "ref_country_id": 9, "created_at": "2025-03-12T18:17:58.000000Z", "updated_at": "2025-03-12T18:23:05.000000Z"}, {"id": 84, "order": 83, "name": "Чеченская республика", "name_pred": "Чеченской республике", "slug": "cecenskaia-respublika", "region_number": 95, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-CE", "region_type": "Респ", "region_type_full": "республика", "region": "Чеченская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:58.000000Z", "updated_at": "2025-03-12T18:23:05.000000Z"}, {"id": 66, "order": 65, "name": "Сахалинская область", "name_pred": "Сахалинской области", "slug": "saxalinskaia-oblast", "region_number": 65, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-SAK", "region_type": "обл", "region_type_full": "область", "region": "Сахалинская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:24:03.000000Z"}, {"id": 65, "order": 64, "name": "Саратовская область", "name_pred": "Саратовской области", "slug": "saratovskaia-oblast", "region_number": 64, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-SAR", "region_type": "обл", "region_type_full": "область", "region": "Саратовская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:02.000000Z"}, {"id": 69, "order": 68, "name": "Тамбовская область", "name_pred": "Тамбовской области", "slug": "tambovskaia-oblast", "region_number": 68, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-TAM", "region_type": "обл", "region_type_full": "область", "region": "Тамбовская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:03.000000Z"}, {"id": 72, "order": 71, "name": "Тульская область", "name_pred": "Тульской области", "slug": "tulskaia-oblast", "region_number": 71, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-TUL", "region_type": "обл", "region_type_full": "область", "region": "Тульская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:23:03.000000Z"}, {"id": 75, "order": 74, "name": "Челябинская область", "name_pred": "Челябинской области", "slug": "celiabinskaia-oblast", "region_number": 74, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-CHE", "region_type": "обл", "region_type_full": "область", "region": "Челябинская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:58.000000Z", "updated_at": "2025-03-12T18:23:03.000000Z"}, {"id": 78, "order": 77, "name": "Еврейская автономная область", "name_pred": "Еврейской автономной области", "slug": "evreiskaia-avtonomnaia-oblast", "region_number": 79, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-YEV", "region_type": "<PERSON><PERSON><PERSON><PERSON>", "region_type_full": "автономная область", "region": "Еврейская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:58.000000Z", "updated_at": "2025-03-12T18:23:04.000000Z"}, {"id": 80, "order": 79, "name": "Ненецкий автономный округ", "name_pred": "Ненецком автономном округе", "slug": "neneckii-avtonomnyi-okrug", "region_number": 83, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-NEN", "region_type": "АО", "region_type_full": "автономный округ", "region": "Ненецкий", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:58.000000Z", "updated_at": "2025-03-12T18:23:04.000000Z"}, {"id": 82, "order": 81, "name": "Ямало-Ненецкий автономный округ", "name_pred": "Ямало-Ненецком автономном округе", "slug": "iamalo-neneckii-avtonomnyi-okrug", "region_number": 89, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-YAN", "region_type": "АО", "region_type_full": "автономный округ", "region": "Ямало-Ненецкий", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:58.000000Z", "updated_at": "2025-03-12T18:23:05.000000Z"}, {"id": 13, "order": 12, "name": "Республика Карелия", "name_pred": "Республике Карелии", "slug": "respublika-kareliia", "region_number": 10, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-KR", "region_type": "Респ", "region_type_full": "республика", "region": "Карелия", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:24:03.000000Z"}, {"id": 56, "order": 55, "name": "Омская область", "name_pred": "Омской области", "slug": "omskaia-oblast", "region_number": 55, "postal_code": null, "country": "Россия", "country_iso_code": "RU", "region_iso_code": "RU-OMS", "region_type": "обл", "region_type_full": "область", "region": "Омская", "city_with_type": null, "city_type": null, "city_type_full": null, "city": null, "lat": null, "lng": null, "ref_country_id": 9, "created_at": "2025-03-12T18:17:57.000000Z", "updated_at": "2025-03-12T18:24:03.000000Z"}]